import { Input } from "@/components/ui/input";
import {
  LivekitInputMessageType,
  PaginationMetadata,
} from "@/shared/interfaces";

export function sanitizeString(text: string): string {
  if (!text) {
    return "";
  }

  // Replace underscores and hyphens with spaces
  let formattedText = text.replace(/[_-]/g, " ");

  // Convert to lowercase and then capitalize the first letter of each word
  formattedText = formattedText
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");

  return formattedText;
}

/**
 * Generates an array of pagination items based on the current page and total pages
 * @param metadata - The pagination metadata containing currentPage, totalPages, etc.
 * @returns An array of numbers and strings, where numbers represent page numbers and strings represent ellipsis
 */
export function generatePaginationItems(
  metadata: PaginationMetadata | null
): (number | string)[] {
  if (!metadata) return [];

  const items = [];
  const { currentPage, totalPages } = metadata;

  // Always show first page
  if (totalPages > 0) {
    items.push(1);
  }

  // Add ellipsis if there's a gap
  if (currentPage > 3) {
    items.push("ellipsis-start");
  }

  // Add pages around current page
  for (
    let i = Math.max(2, currentPage - 1);
    i <= Math.min(totalPages - 1, currentPage + 1);
    i++
  ) {
    if (!items.includes(i)) {
      items.push(i);
    }
  }

  // Add ellipsis if there's a gap
  if (currentPage < totalPages - 2) {
    items.push("ellipsis-end");
  }

  // Always show last page if it's different from first
  if (totalPages > 1 && !items.includes(totalPages)) {
    items.push(totalPages);
  }

  return items;
}
