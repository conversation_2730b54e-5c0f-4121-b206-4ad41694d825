"use client";

import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { useState } from "react";

interface Props {
  children: React.ReactNode;
}

export default function QueryProvider({ children }: Props) {
  const [queryClient] = useState(() => new QueryClient()); // To ensure that the query client is not recreated on every render
  return (
    <QueryClientProvider client={queryClient}> {children} </QueryClientProvider>
  );
}
