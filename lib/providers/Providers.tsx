"use client";

import React from "react";
import { ThemeProvider } from "./ThemeProvider";
import { Toaster } from "sonner";
import QueryProvider from "./QueryProvider";
import { SessionInitializer } from "@/components/auth/SessionInitializer";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SessionInitializer>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <QueryProvider>
          <Toaster />
          {children}
        </QueryProvider>
      </ThemeProvider>
    </SessionInitializer>
  );
}
