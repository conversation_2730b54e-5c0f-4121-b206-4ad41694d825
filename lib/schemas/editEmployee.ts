import {
  EmployeeDepartment,
  EmployeeTone,
  EmployeeVisibility,
  ModelProvider,
} from "@/shared/enums";
import { z } from "zod";

export const editEmployeeProfileSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(20, "Name must be less than 20 characters"),
  agent_topic_type: z
    .string()
    .min(1, "Role is required")
    .max(20, "Role must be less than 20 characters"),
  description: z
    .string()
    .min(10, "Description must be at least 10 characters")
    .max(500, "Description must be less than 500 characters"),
  avatar: z.string(),
  category: z.nativeEnum(EmployeeDepartment),
  ruh_credentials: z.boolean(),
  visibility: z.nativeEnum(EmployeeVisibility),
  model_provider: z.nativeEnum(ModelProvider).optional(),
  model_name: z.string().optional(),
  model_api_key: z.string().optional(),
  system_message: z
    .string()
    .min(10, "Instructions must be at least 10 characters")
    .max(1500, "Instructions must be less than 1500 characters"),
  tone: z.nativeEnum(EmployeeTone),
  files: z.array(z.string()).optional(),
  urls: z.array(z.string()).optional(),
  mcp_server_ids: z.array(z.string()).optional(),
  workflow_ids: z.array(z.string()).optional(),
});

export type EditEmployeeProfileSchema = z.infer<
  typeof editEmployeeProfileSchema
>;
