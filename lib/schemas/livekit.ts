import { workflowStatus } from "@/shared/enums";
import { Status } from "@/shared/enums";
import { z } from "zod";

export const livekitMessageSchema = z.object({
  content: z.string(),
  workflow: z.any().nullable(),
});

export const livekitTranscriptionOutputSchema = z.object({
  is_initial_response: z.boolean(),
  content: z.string(),
  error: z.boolean().optional(),
});

export const livekitWorkflowOutputSchema = z.object({
  transition_id: z.string(),
  node_id: z.string(),
  tool_name: z.string(),
  result: z.any(),
  approval_required: z.boolean(),
  status: z.nativeEnum(Status),
  workflow_status: z.nativeEnum(workflowStatus),
});

export const livekitAgentOutputSchema = z.object({
  status: z.nativeEnum(Status),
  workflow_status: z.nativeEnum(workflowStatus),
  result: z.any(),
  sequence: z.number(),
});

export type LivekitAgentOutputType = z.infer<typeof livekitAgentOutputSchema>;

export type LivekitWorkflowOutputType = z.infer<
  typeof livekitWorkflowOutputSchema
>;

export type LivekitTranscriptionOutputType = z.infer<
  typeof livekitTranscriptionOutputSchema
>;

export type LivekitMessageType = z.infer<typeof livekitMessageSchema>;
