import { act } from "@testing-library/react";
import { useEmployeeEditStore } from "../useEmployeeEditStore";

const initialState = useEmployeeEditStore.getState();

describe("useEmployeeEditStore", () => {
  beforeEach(() => {
    useEmployeeEditStore.setState(initialState, true);
  });

  it("should have initial state with isEditing as false", () => {
    const state = useEmployeeEditStore.getState();
    expect(state.isEditing).toBeFalsy();
    expect(state.toggleEditMode).toBeInstanceOf(Function);
    expect(state.resetEditMode).toBeInstanceOf(Function);
  });

  it("should toggle isEditing state when toggleEditMode is called", () => {
    const { toggleEditMode } = useEmployeeEditStore.getState();

    act(() => {
      toggleEditMode();
    });

    expect(useEmployeeEditStore.getState().isEditing).toBeTruthy();

    act(() => {
      toggleEditMode();
    });

    expect(useEmployeeEditStore.getState().isEditing).toBeFalsy();
  });

  it("should reset isEditing state to false when resetEditMode is called", () => {
    const { toggleEditMode, resetEditMode } = useEmployeeEditStore.getState();

    act(() => {
      toggleEditMode();
    });
    expect(useEmployeeEditStore.getState().isEditing).toBeTruthy();

    act(() => {
      resetEditMode();
    });
    expect(useEmployeeEditStore.getState().isEditing).toBeFalsy();
  });
});