import { act } from "@testing-library/react";
import { tools } from "@/shared/constants";
import { useToolStore } from "@/hooks/use-workflow";

// Define test constants
const testAgentId = "test-agent-1";
const testToolId = tools.length > 0 ? tools[0].id : "test-tool-1";

// Get the initial state *once* to reset before each test
const initialState = useToolStore.getState();

describe("useToolStore", () => {
  // Reset the store to its initial state before each test
  beforeEach(() => {
    useToolStore.setState(initialState, true); // Use replace=true for clean reset
  });

  it("should have initial state with empty agentToolSelections", () => {
    const state = useToolStore.getState();
    // Verify the initial state has empty agentToolSelections
    expect(state.agentToolSelections).toEqual({});
    expect(state.setCurrentToolId).toBeInstanceOf(Function);
    expect(state.getCurrentToolId).toBeInstanceOf(Function);
  });

  it("should set a tool ID for a specific agent", () => {
    const { setCurrentToolId, getCurrentToolId } = useToolStore.getState();

    act(() => {
      setCurrentToolId(testAgentId, testToolId);
    });

    // Check that the tool ID was set for the specific agent
    const toolId = getCurrentToolId(testAgentId);
    expect(toolId).toBe(testToolId);

    // Check that the agentToolSelections map was updated correctly
    const state = useToolStore.getState();
    expect(state.agentToolSelections[testAgentId]).toBe(testToolId);
  });

  it("should set tool ID to null for a specific agent", () => {
    const { setCurrentToolId, getCurrentToolId } = useToolStore.getState();

    // First set a tool ID
    act(() => {
      setCurrentToolId(testAgentId, testToolId);
    });

    // Then set it to null
    act(() => {
      setCurrentToolId(testAgentId, null);
    });

    // Check that the tool ID was set to null
    const toolId = getCurrentToolId(testAgentId);
    expect(toolId).toBeNull();
  });

  it("should handle multiple agents with different tool selections", () => {
    const anotherAgentId = "test-agent-2";
    const anotherToolId = "test-tool-2";

    const { setCurrentToolId, getCurrentToolId } = useToolStore.getState();

    // Set different tools for different agents
    act(() => {
      setCurrentToolId(testAgentId, testToolId);
      setCurrentToolId(anotherAgentId, anotherToolId);
    });

    // Check that each agent has its own tool selection
    expect(getCurrentToolId(testAgentId)).toBe(testToolId);
    expect(getCurrentToolId(anotherAgentId)).toBe(anotherToolId);

    // Verify the state structure
    const state = useToolStore.getState();
    expect(state.agentToolSelections).toEqual({
      [testAgentId]: testToolId,
      [anotherAgentId]: anotherToolId,
    });
  });
});
