import { act } from "@testing-library/react";
import { useEmployeeStore } from "@/hooks/use-employee";

// Get the initial state *once* to reset before each test
const initialState = useEmployeeStore.getState();

describe("useEmployeeStore", () => {
  // Reset the store to its initial state before each test
  beforeEach(() => {
    // Use setState with replace=true to ensure a clean slate
    useEmployeeStore.setState(initialState, true);
  });

  it("should have initial state with currentEmployeeId as null", () => {
    const state = useEmployeeStore.getState();
    expect(state.currentEmployeeId).toBeNull();
    // Optionally check if the setter function exists
    expect(state.setCurrentEmployeeId).toBeInstanceOf(Function);
  });

  it("should update currentEmployeeId when setCurrentEmployeeId is called with a string ID", () => {
    const testId = "employee-123";
    const { setCurrentEmployeeId } = useEmployeeStore.getState();

    // Use 'act' to wrap state updates, ensuring they are processed
    act(() => {
      setCurrentEmployeeId(testId);
    });

    const updatedState = useEmployeeStore.getState();
    expect(updatedState.currentEmployeeId).toBe(testId);
  });

  it("should update currentEmployeeId to null when setCurrentEmployeeId is called with null", () => {
    const testId = "employee-456";
    const { setCurrentEmployeeId } = useEmployeeStore.getState();

    // First, set an ID
    act(() => {
      setCurrentEmployeeId(testId);
    });
    // Verify it was set
    expect(useEmployeeStore.getState().currentEmployeeId).toBe(testId);

    // Now, set it back to null
    act(() => {
      setCurrentEmployeeId(null);
    });

    const finalState = useEmployeeStore.getState();
    expect(finalState.currentEmployeeId).toBeNull();
  });
});
