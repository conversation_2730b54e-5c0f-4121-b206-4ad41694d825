import { act } from "@testing-library/react";
import { useNotificationStore } from "@/hooks/use-notification";

// Get the initial state *once* to reset before each test
const initialState = useNotificationStore.getState();

describe("useNotificationStore", () => {
  // Reset the store to its initial state before each test
  beforeEach(() => {
    // Use setState with replace=true to ensure a clean slate
    useNotificationStore.setState(initialState, true);
  });

  it("should initialize with isNotificationOpen as false", () => {
    const state = useNotificationStore.getState();
    expect(state.isNotificationOpen).toBe(false);
  });

  it("should toggle notification state", () => {
    // Initial state should be false
    expect(useNotificationStore.getState().isNotificationOpen).toBe(false);

    // Toggle to true
    act(() => {
      useNotificationStore.getState().toggleNotification();
    });
    expect(useNotificationStore.getState().isNotificationOpen).toBe(true);

    // Toggle back to false
    act(() => {
      useNotificationStore.getState().toggleNotification();
    });
    expect(useNotificationStore.getState().isNotificationOpen).toBe(false);
  });

  it("should open notifications", () => {
    // Initial state should be false
    expect(useNotificationStore.getState().isNotificationOpen).toBe(false);

    // Open notifications
    act(() => {
      useNotificationStore.getState().openNotification();
    });
    expect(useNotificationStore.getState().isNotificationOpen).toBe(true);
  });

  it("should close notifications", () => {
    // Set initial state to true
    act(() => {
      useNotificationStore.setState({ isNotificationOpen: true });
    });
    expect(useNotificationStore.getState().isNotificationOpen).toBe(true);

    // Close notifications
    act(() => {
      useNotificationStore.getState().closeNotification();
    });
    expect(useNotificationStore.getState().isNotificationOpen).toBe(false);
  });
});
