import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { User } from "@/shared/interfaces"; // Ensure User interface is defined here

// Define the state structure
interface UserState {
  user: User | null;
  isLoadingAuth: boolean; // Added to manage loading UI during auth check
}

// Define the actions available on the store
interface UserActions {
  setUser: (userData: User | null) => void;
  clearUser: () => void;
  setIsLoadingAuth: (loading: boolean) => void; // Added action to set loading state
}

// Create the Zustand store
export const useUserStore = create<UserState & UserActions>()(
  persist(
    (set) => ({
      // Initial state
      user: null,
      isLoadingAuth: true, // Default to true on app load, so SessionInitializer runs its checks

      // Actions
      setUser: (userData) =>
        set((state) => ({
          // Merge existing user data with new data if user exists
          user: state.user ? { ...state.user, ...userData } : userData,
          isLoadingAuth: false,
        })),

      clearUser: () =>
        set({
          user: null,
          isLoadingAuth: false, // Ensure isLoadingAuth is reset on clear
        }),

      setIsLoadingAuth: (loading) => set({ isLoadingAuth: loading }),
    }),
    {
      name: "user-session-storage", // Unique name for localStorage
      storage: createJSONStorage(() => localStorage), // For web localStorage
      // Optionally, partialize to only persist the user object, not isLoadingAuth
      // partialize: (state) => ({ user: state.user }),
    }
  )
);
