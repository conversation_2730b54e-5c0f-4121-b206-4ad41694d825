// This hook will be used to store and set the id of the current workflow that is being used in the chat of employee.
// It maintains separate workflow selections for each agent to prevent conflicts when chatting with multiple agents.

import { WorkflowField } from "@/shared/interfaces";
import { create } from "zustand";

interface WorkflowState {
  // Map of agent IDs to their selected workflow IDs

  openWorkflowStartingForm: boolean;
  setOpenWorkflowStartingForm: (open: boolean) => void;

  agentWorkflowSelections: Record<string, string | null>;
  setCurrentWorkflow: (agentId: string, workflowId: string | null) => void;
  getCurrentWorkflow: (agentId: string) => string | null;

  currentWorkflowName: string | null;
  setCurrentWorkflowName: (workflowName: string | null) => void;

  workflowValues: WorkflowField[]; // Use the new WorkflowField type

  setWorkflowValues: (
    initialFields: Omit<WorkflowField, "value">[] // Expect fields without 'value' initially
  ) => void;

  updateWorkflowFieldValue: (fieldId: string, value: string) => void; // New updater function
}

export const useWorkflowStore = create<WorkflowState>((set, get) => ({
  agentWorkflowSelections: {}, // Store workflow selections per agent
  openWorkflowStartingForm: false,
  setOpenWorkflowStartingForm: (open) =>
    set({ openWorkflowStartingForm: open }),
  setCurrentWorkflow: (agentId, workflowId) =>
    set((state) => ({
      agentWorkflowSelections: {
        ...state.agentWorkflowSelections,
        [agentId]: workflowId,
      },
    })),
  getCurrentWorkflow: (agentId) =>
    get().agentWorkflowSelections[agentId] || null,
  currentWorkflowName: null,
  setCurrentWorkflowName: (workflowName) =>
    set({ currentWorkflowName: workflowName }),
  workflowValues: [],
  setWorkflowValues: (initialFields) =>
    set({
      workflowValues: initialFields.map((field) => ({
        ...field,
        value: field.type === "array" ? [] : "", // Initialize based on type
      })),
    }),
  updateWorkflowFieldValue: (fieldId, newValue) =>
    set((state) => ({
      workflowValues: state.workflowValues.map((item) =>
        item.field === fieldId ? { ...item, value: newValue } : item
      ),
    })),
}));
