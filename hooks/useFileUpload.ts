"use client";

import { useState, useRef } from "react";
import { gcsApi, uploadToGCS } from "@/app/api/gcs";
import { toast } from "sonner";

export type FileType = "image" | "document" | "audio" | "video" | "any";

interface UseFileUploadOptions {
  onSuccess?: (url: string) => void;
  onError?: (error: Error) => void;
  filePath?: string;
  fileType?: FileType;
  acceptedFormats?: string;
  maxSizeMB?: number;
  customSuccessMessage?: string;
  customErrorMessage?: string;
  onUploadStateChange?: (isUploading: boolean) => void;
}

/**
 * Custom hook for handling file uploads to Google Cloud Storage
 * @param options Configuration options for the upload
 * @returns Object containing upload functions and state
 */
export const useFileUpload = (options?: UseFileUploadOptions) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedUrl, setUploadedUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Default options
  const {
    onSuccess,
    onError,
    filePath = "uploads",
    fileType = "any",
    acceptedFormats,
    maxSizeMB = 10, // Default 10MB max size
    customSuccessMessage = "File uploaded successfully",
    customErrorMessage,
    onUploadStateChange,
  } = options || {};

  /**
   * Get the accept attribute value for the file input based on fileType
   */
  const getAcceptValue = (): string => {
    if (acceptedFormats) return acceptedFormats;

    switch (fileType) {
      case "image":
        return "image/*";
      case "document":
        return ".pdf,.doc,.docx,.txt,.rtf,.xls,.xlsx,.ppt,.pptx";
      case "audio":
        return "audio/*";
      case "video":
        return "video/*";
      case "any":
      default:
        return "";
    }
  };

  /**
   * Validate if the file meets the requirements
   */
  const validateFile = (file: File): { valid: boolean; message?: string } => {
    // Check file size
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSizeMB) {
      return {
        valid: false,
        message: `File size exceeds the maximum limit of ${maxSizeMB}MB`,
      };
    }

    // Check file type if specified
    if (fileType !== "any") {
      switch (fileType) {
        case "image":
          if (!file.type.startsWith("image/")) {
            return { valid: false, message: "Please select an image file" };
          }
          break;
        case "document":
          const docTypes = [
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument",
            "text/",
          ];
          if (!docTypes.some((type) => file.type.startsWith(type))) {
            return { valid: false, message: "Please select a document file" };
          }
          break;
        case "audio":
          if (!file.type.startsWith("audio/")) {
            return { valid: false, message: "Please select an audio file" };
          }
          break;
        case "video":
          if (!file.type.startsWith("video/")) {
            return { valid: false, message: "Please select a video file" };
          }
          break;
      }
    }

    return { valid: true };
  };

  /**
   * Trigger the file input click
   */
  const triggerFileSelect = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  /**
   * Upload a file directly without using the file input
   * Useful for programmatic uploads or drag and drop
   */
  const uploadFile = async (file: File): Promise<string | null> => {
    // Validate the file
    const validation = validateFile(file);
    if (!validation.valid) {
      return null;
    }

    setIsUploading(true);
    if (onUploadStateChange) {
      onUploadStateChange(true);
    }

    try {
      // Generate a unique filename to avoid collisions
      const timestamp = new Date().getTime();
      const uniqueFileName = `${timestamp}-${file.name}`;

      // Get presigned URL from the server
      const presignedUrlResponse = await gcsApi.getPresignedUrl(
        uniqueFileName,
        file.type,
        filePath
      );

      if (!presignedUrlResponse.url) {
        throw new Error("No presigned URL received from server");
      }

      // Upload the file to GCS using the presigned URL
      const publicUrl = await uploadToGCS(presignedUrlResponse.url, file);

      // Update state with the uploaded URL
      setUploadedUrl(publicUrl);

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(publicUrl);
      }

      toast.success(customSuccessMessage);

      return publicUrl;
    } catch (error) {
      console.error("File upload error:", error);
      const errorMessage =
        customErrorMessage ||
        (error instanceof Error ? error.message : "Failed to upload file");

      // Call error callback if provided
      if (onError && error instanceof Error) {
        onError(error);
      }

      return null;
    } finally {
      setIsUploading(false);
      if (onUploadStateChange) {
        onUploadStateChange(false);
      }
    }
  };

  /**
   * Handle file selection from input and upload
   */
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    await uploadFile(file);

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return {
    fileInputRef,
    isUploading,
    uploadedUrl,
    triggerFileSelect,
    handleFileChange,
    uploadFile,
    acceptValue: getAcceptValue(),
  };
};
