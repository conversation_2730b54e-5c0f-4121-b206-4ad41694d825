import { create } from "zustand";

export interface TranscriptionMessage {
  id: string | number;
  text: string;
  type: "agent" | "user";
  firstReceivedTime: number;
  isFinal?: boolean; // Indicates if this is a final message or part of a stream
  streamId?: string; // Used to group streamed messages together
}

interface TranscriptionState {
  messages: TranscriptionMessage[];
  streamingAgentMessage: string | null; // Current streaming message content
  streamingMessageId: string | null; // ID of the current streaming message
  addMessage: (message: TranscriptionMessage) => void;
  updateStreamingMessage: (text: string, id: string) => void;
  finalizeStreamingMessage: () => void;
  addMessages: (messages: TranscriptionMessage[]) => void;
  clearMessages: () => void;
}

export const useTranscriptionStore = create<TranscriptionState>((set, get) => ({
  messages: [],
  streamingAgentMessage: null,
  streamingMessageId: null,

  // Add a new message
  addMessage: (message) => {
    // If it's a user message or a final agent message, add it normally
    if (message.type === "user" || message.isFinal) {
      set((state) => ({
        messages: [...state.messages, message],
      }));
      return;
    }

    // If it's a streaming agent message, update the streaming state
    if (message.type === "agent") {
      set({
        streamingAgentMessage: message.text,
        streamingMessageId:
          typeof message.id === "string" ? message.id : String(message.id),
      });
    }
  },

  // Update the current streaming message
  updateStreamingMessage: (text, id) => {
    set({
      streamingAgentMessage: text,
      streamingMessageId: id,
    });
  },

  // Finalize the current streaming message and add it to messages
  finalizeStreamingMessage: () => {
    const { streamingAgentMessage, streamingMessageId } = get();

    if (streamingAgentMessage && streamingMessageId) {
      const finalMessage: TranscriptionMessage = {
        id: streamingMessageId,
        text: streamingAgentMessage,
        type: "agent",
        firstReceivedTime: Date.now(),
        isFinal: true,
      };

      set((state) => ({
        messages: [...state.messages, finalMessage],
        streamingAgentMessage: null,
        streamingMessageId: null,
      }));
    }
  },

  // Add multiple messages at once
  addMessages: (newMessages) =>
    set((state) => {
      // Create a map of existing messages by ID to avoid duplicates
      const existingIds = new Set(state.messages.map((m) => m.id));

      // Filter out messages that already exist
      const uniqueNewMessages = newMessages.filter(
        (m) => !existingIds.has(m.id)
      );

      // Process streaming messages
      let updatedStreamingMessage = state.streamingAgentMessage;
      let updatedStreamingId = state.streamingMessageId;

      // Find the latest agent message that's not final
      const latestAgentMessage = uniqueNewMessages
        .filter((m) => m.type === "agent" && !m.isFinal)
        .sort((a, b) => b.firstReceivedTime - a.firstReceivedTime)[0];

      if (latestAgentMessage) {
        updatedStreamingMessage = latestAgentMessage.text;
        updatedStreamingId =
          typeof latestAgentMessage.id === "string"
            ? latestAgentMessage.id
            : String(latestAgentMessage.id);
      }

      // Filter out non-final agent messages from the list to add
      const messagesToAdd = uniqueNewMessages.filter(
        (m) => m.type === "user" || m.isFinal
      );

      // Combine existing and new messages, then sort by time
      const allMessages = [...state.messages, ...messagesToAdd].sort(
        (a, b) => a.firstReceivedTime - b.firstReceivedTime
      );

      return {
        messages: allMessages,
        streamingAgentMessage: updatedStreamingMessage,
        streamingMessageId: updatedStreamingId,
      };
    }),

  // Clear all messages and streaming state
  clearMessages: () =>
    set({
      messages: [],
      streamingAgentMessage: null,
      streamingMessageId: null,
    }),
}));
