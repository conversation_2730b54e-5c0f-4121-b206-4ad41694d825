// This hook will be used to globally store and manage notification state

import { create } from "zustand";

// Define the notification state interface
interface NotificationState {
  isNotificationOpen: boolean;
  toggleNotification: () => void;
  openNotification: () => void;
  closeNotification: () => void;
}

// Create the notification store
export const useNotificationStore = create<NotificationState>((set) => ({
  isNotificationOpen: false,
  toggleNotification: () => set((state) => ({ isNotificationOpen: !state.isNotificationOpen })),
  openNotification: () => set({ isNotificationOpen: true }),
  closeNotification: () => set({ isNotificationOpen: false }),
}));
