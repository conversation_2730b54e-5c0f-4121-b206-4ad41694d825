// This hook will be used to globally store and set the id of the current employee that is being used used.

import { create } from "zustand";

interface EmployeeState {
  currentEmployeeId: string | null;
  setCurrentEmployeeId: (employeeId: string | null) => void;
}

export const useEmployeeStore = create<EmployeeState>((set) => ({
  currentEmployeeId: null,
  setCurrentEmployeeId: (employeeId) => set({ currentEmployeeId: employeeId }),
}));
