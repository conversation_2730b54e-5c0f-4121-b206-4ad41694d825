import { CreateEmployeeProfileSchema } from "@/lib/schemas/createEmployee";
import { EmployeeTone, EmployeeVisibility } from "@/shared/enums";
import { ModelProvider } from "@/shared/enums";
import { EmployeeDepartment } from "@/shared/enums";
import { create } from "zustand";

interface EmployeeCreateState {
  openModal: () => void;
  closeModal: () => void;
  isModalOpen: boolean;
  data: CreateEmployeeProfileSchema;
  setData: (data: Partial<CreateEmployeeProfileSchema>) => void;
  formStep: number;
  setFormStep: (step: number) => void;
  reset: () => void;
}

// Define initial state
const initialState: Pick<
  EmployeeCreateState,
  "isModalOpen" | "data" | "formStep"
> = {
  isModalOpen: false,
  data: {
    name: "",
    agent_topic_type: "",
    description: "",
    avatar: "",
    department: EmployeeDepartment.ENGINEERING,
    ruh_credentials: true,
    visibility: EmployeeVisibility.PUBLIC,
    model_provider: ModelProvider.OPENAI,
    model_name: "",
    model_api_key: "",
    system_message: "",
    tone: EmployeeTone.FRIENDLY,
    files: [],
    urls: [],
    mcp_server_ids: [],
    workflow_ids: [],
  },
  formStep: 1,
};

export const useEmployeeCreateStore = create<EmployeeCreateState>((set) => ({
  ...initialState,
  openModal: () => set({ isModalOpen: true }),
  closeModal: () => set({ isModalOpen: false }),
  setData: (newData) =>
    set((state) => ({
      ...state,
      data: { ...state.data, ...newData },
    })),
  setFormStep: (step) => set({ formStep: step }),
  reset: () => set(initialState),
}));
