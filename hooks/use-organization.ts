import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { Organization } from "@/shared/interfaces"; // Make sure Organization interface is correctly defined

// Define the state structure
interface OrgState {
  organization: Organization[] | null;
  isLoadingOrganization: boolean;
}

// Define the actions available on the store
interface OrgActions {
  setOrganization: (orgData: Organization[] | null) => void;
  clearOrganization: () => void;
  setIsLoadingOrganization: (loading: boolean) => void;
}

// Create Zustand store
export const useOrgStore = create<OrgState & OrgActions>()(
  persist(
    (set, get) => ({
      // Initial state
      organization: null,
      isLoadingOrganization: true,

      // Set organization
      setOrganization: (orgData) => {
        if (!orgData) {
          set({ organization: null, isLoadingOrganization: false });
          return;
        }
        const current = get().organization;
        set({
          organization: orgData ? orgData : current,
          isLoadingOrganization: false,
        });
      },

      // Clear organization
      clearOrganization: () => {
        set({
          organization: null,
          isLoadingOrganization: false,
        });
      },

      // Set loading state
      setIsLoadingOrganization: (loading) =>
        set({ isLoadingOrganization: loading }),
    }),
    {
      name: "organization-session-storage",
      storage: createJSONStorage(() => localStorage),
      // Optional: persist only `organization`
      // partialize: (state) => ({ organization: state.organization }),
    }
  )
);
