import { Suspense } from "react";
import { Logo } from "@/components/shared/Logo";
import { ImageCarousel } from "../_components/ImageCarousel";
import { AuthButton } from "../_components/AuthButton";

const LoginPage = () => {
  return (
    <div className="min-h-screen flex md:flex-row">
      {/* Left Side - Login Form */}
      <div className="w-full lg:w-1/2 p-8 md:p-12 lg:p-16 flex flex-col items-center justify-center gap-6 bg-brand-overlay border-r border-gray-200/90 dark:border-brand-card rounded-r-xl">
        <div>
          <Logo />
        </div>

        <div className="max-w-md w-full flex flex-col justify-center gap-6">
          <div className="flex flex-col  justify-center gap-16">
            <div>
              <h1 className=" text-2xl font-bold mb-2 text-brand-primary-font text-center font-primary">
                Log In & Build Your AI Workforce
              </h1>
              <p className="text-brand-secondary-font text-sm text-center ">
                Join AI Workforce — Automate Your Tasks with Smart AI Agents
              </p>
            </div>
            <Suspense fallback={<div>Loading...</div>}>
              <AuthButton />
            </Suspense>
          </div>
        </div>
      </div>

      {/* Right Side - Image Carousel */}
      <div className="w-full md:w-1/2  hidden lg:block">
        <div className="h-full flex items-center justify-center p-8">
          <ImageCarousel />
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
