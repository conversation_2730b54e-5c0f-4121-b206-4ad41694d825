"use client";

import { useSearchParams } from "next/navigation";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { authRoute } from "@/shared/routes";
import { useRouter } from "next/navigation";

export const AuthButton = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const inviteToken = searchParams.get("token");

  const handleLogin = () => {
    if (inviteToken) {
      localStorage.setItem("inviteToken", inviteToken);
    }
    router.push(authRoute);
  };

  return (
    <div>
      <PrimaryButton onClick={handleLogin}>Login</PrimaryButton>
    </div>
  );
};
