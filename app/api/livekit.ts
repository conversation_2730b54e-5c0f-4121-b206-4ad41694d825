// Removed: import api from "@/services/axios";

import { getAccessToken } from "@/services/authCookies";

/**
 * LiveKit API functions
 */
export const livekitApi = {
  /**
   * Get a LiveKit token for connecting to a room
   * @returns Promise with the token string
   */
  getToken: async (
    agentId: string,
    chatType: string,
    conversationId: string
  ): Promise<string> => {
    const authToken = await getAccessToken();

    const url = process.env.NEXT_PUBLIC_API_URL!;
    const token = authToken;
    const body = {
      agent_id: agentId,
      chat_type: chatType,
      conversation_id: conversationId,
    };

    try {
      const response = await fetch(`${url}/livekit/room/create`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        // Attempt to read error details from response body
        let errorDetails = "Request failed";
        try {
          const errorData = await response.json();
          errorDetails =
            errorData.message || errorData.detail || JSON.stringify(errorData);
        } catch (parseError) {
          // If parsing fails, use status text
          errorDetails = response.statusText;
        }
        throw new Error(`API Error (${response.status}): ${errorDetails}`);
      }

      const data = await response.json();

      // Updated token extraction logic
      if (!data?.token) {
        console.error("Unexpected response structure or missing token:", data);
        throw new Error("No token returned from API");
      }

      return data.token;
    } catch (error: any) {
      console.error("Failed to get LiveKit token:", error);
      // Re-throw a cleaner error message
      throw new Error(
        error.message ||
          "Failed to get LiveKit token due to a network or unexpected error"
      );
    }
  },
};
