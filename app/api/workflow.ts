import api from "@/services/axios";
import {
  PaginatedWorkflowResponse,
  WorkflowsByIdsResponse,
} from "@/shared/interfaces";

export const workflowApi = {
  /**
   * Get workflows for the current user with pagination
   * @param page The page number for pagination (default: 1)
   * @param pageSize The number of workflows per page (default: 10)
   * @returns Promise resolving to the paginated list of workflows
   */
  getWorkflowsByUser: async (
    page?: number,
    pageSize?: number
  ): Promise<PaginatedWorkflowResponse> => {
    try {
      const response = await api.get<PaginatedWorkflowResponse>("/workflows", {
        params: {
          page,
          page_size: pageSize,
        },
      });

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get workflows"
      );
    }
  },

  /**
   * Get multiple Workflows by their IDs
   * @param ids Array of Workflows IDs to retrieve
   * @returns Promise resolving to the MCPs matching the provided IDs
   */
  getWorkflowsByIds: async (ids: string[]): Promise<WorkflowsByIdsResponse> => {
    try {
      const response = await api.post<WorkflowsByIdsResponse>(
        "/workflows/by-ids",
        {
          ids,
        }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get Workflows by IDs"
      );
    }
  },
};
