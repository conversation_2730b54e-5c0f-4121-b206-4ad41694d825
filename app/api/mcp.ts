import api from "@/services/axios";
import { PaginatedMCPResponse, MCPsByIdsResponse } from "@/shared/interfaces";

export const mcpApi = {
  /**
   * Get MCPs for the current user with pagination
   * @param page The page number for pagination (default: 1)
   * @param pageSize The number of MCPs per page (default: 10)
   * @returns Promise resolving to the paginated list of MCPs
   */
  getMcpServersByUser: async (
    page?: number,
    pageSize?: number
  ): Promise<PaginatedMCPResponse> => {
    try {
      const response = await api.get<PaginatedMCPResponse>("/mcps", {
        params: {
          page,
          page_size: pageSize,
        },
      });

      console.log("response", response);
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get MCP servers"
      );
    }
  },

  /**
   * Get multiple MCPs by their IDs
   * @param ids Array of MCP IDs to retrieve
   * @returns Promise resolving to the MCPs matching the provided IDs
   */
  getMcpsByIds: async (ids: string[]): Promise<MCPsByIdsResponse> => {
    try {
      const response = await api.post<MCPsByIdsResponse>("/mcps/by-ids", {
        ids,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get MCPs by IDs"
      );
    }
  },
};
