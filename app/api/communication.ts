import api from "@/services/axios";
import {
  ConversationCreate,
  ConversationResponse,
  ConversationList,
  GetConversationsParams,
  MessageList,
  GetMessagesParams,
  ChannelType,
  ChatType,
} from "@/shared/interfaces";

export const communicationApi = {
  /**
   * Create a new conversation
   * @param agentId The ID of the agent to create conversation with
   * @returns Promise resolving to the conversation ID
   */
  createConversation: async (agentId: string): Promise<string> => {
    try {
      const payload: ConversationCreate = {
        title: "chat",
        channel: ChannelType.WEB,
        chatType: ChatType.SINGLE,
        summary: "summary",
        agentId,
      };

      const response = await api.post<ConversationResponse>(
        "/communication/conversation",
        payload
      );

      return response.data.id;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to create conversation"
      );
    }
  },

  /**
   * Get conversation details by ID
   * @param conversationId The ID of the conversation to retrieve
   * @returns Promise resolving to the conversation details
   */
  getConversation: async (
    conversationId: string
  ): Promise<ConversationResponse> => {
    try {
      const response = await api.get<ConversationResponse>(
        `/communication/conversation/${conversationId}`
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get conversation"
      );
    }
  },

  /**
   * Delete a conversation by ID
   * @param conversationId The ID of the conversation to delete
   * @returns Promise resolving when the conversation is deleted
   */
  deleteConversation: async (conversationId: string): Promise<void> => {
    try {
      await api.delete(`/communication/conversation/${conversationId}`);
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to delete conversation"
      );
    }
  },

  /**
   * Get paginated list of conversations
   * @param params Parameters for filtering and pagination
   * @returns Promise resolving to the paginated list of conversations
   */
  getConversations: async (
    params: GetConversationsParams
  ): Promise<ConversationList> => {
    try {
      const { agentId, channel, chatType, page = 1, limit = 10 } = params;

      const response = await api.get<ConversationList>(
        "/communication/conversations",
        {
          params: {
            agentId,
            channel,
            chatType,
            page,
            limit,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get conversations"
      );
    }
  },

  /**
   * Get paginated list of messages for a conversation
   * @param params Parameters for filtering and pagination
   * @returns Promise resolving to the paginated list of messages
   */
  getMessages: async (params: GetMessagesParams): Promise<MessageList> => {
    try {
      const { conversationId, page = 1, limit = 10 } = params;

      const response = await api.get<MessageList>(
        `/communication/messages/${conversationId}`,
        {
          params: {
            page,
            limit,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get messages"
      );
    }
  },
};
