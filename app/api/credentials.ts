import api from "@/services/axios";

interface CredentialInfo {
  id: string;
  key_name: string;
  description: string | null;
  value: string;
  created_at: string;
  last_used_at: string;
}

interface CredentialListResponse {
  success: boolean;
  message: string;
  credentials: CredentialInfo[];
}

interface CredentialCreate {
  key_name: string;
  value: string;
  description?: string | null;
}

interface CredentialResponse {
  success: boolean;
  message: string;
  id?: string | null;
  key_name?: string | null;
  value?: string | null;
}

interface CredentialDeleteResponse {
  success: boolean;
  message: string;
}

export const mcpApi = {
  /**
   * Get all credentials for the current user
   * @returns Promise resolving to the list of credentials
   */
  getCredentials: async (): Promise<CredentialListResponse> => {
    try {
      const response = await api.get<CredentialListResponse>("/credentials");
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get credentials"
      );
    }
  },

  /**
   * Create a new credential
   * @param data The credential data to create
   * @returns Promise resolving to the created credential response
   */
  createCredential: async (
    data: CredentialCreate
  ): Promise<CredentialResponse> => {
    try {
      const response = await api.post<CredentialResponse>("/credentials", data);
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to create credential"
      );
    }
  },

  /**
   * Delete a credential by its ID
   * @param credentialId The ID of the credential to delete
   * @returns Promise resolving to the delete response
   */
  deleteCredential: async (
    credentialId: string
  ): Promise<CredentialDeleteResponse> => {
    try {
      const response = await api.delete<CredentialDeleteResponse>(
        `/credentials/${credentialId}`
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to delete credential"
      );
    }
  },
};
