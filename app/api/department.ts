import api from "@/services/axios";
import { Organization } from "@/shared/interfaces";

export const departmentApi = {
  /* =================== Organization Details ================== */

  /**
   * Get Department List
   * @description Requires valid JWT Bearer token in Authorization header
   */
  getDepartmentList: async (): Promise<Organization> => {
    try {
      const response = await api.get("/organizations/getUserOrganisations");
      return response?.data?.organizations || [];
    } catch (error: any) {
      // Let axios interceptor handle auth errors (401/403) for token refresh
      // Only handle non-auth errors here
      switch (error.response?.status) {
        case 404:
          throw new Error("User not found");
        case 500:
          throw new Error("Internal server error");
        default:
          throw new Error(
            error.response?.data?.detail || "Failed to fetch user information"
          );
      }
    }
  },
};
