"use client";

import Link from "next/link";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";

const areaSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(80, "Must be 80 characters or less")
    .regex(
      /^([a-z0-9\-])+$/,
      "Must be lowercase and without spaces or periods"
    ),
  purpose: z.string().optional(),
  invites: z.string().optional(),
  instructions: z.string().optional(),
  isPrivate: z.boolean().optional(),
});

const CreateDepartment = () => {
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof areaSchema>>({
    resolver: zodResolver(areaSchema),
    defaultValues: {
      name: "",
      purpose: "",
      invites: "",
      instructions: "",
      isPrivate: false,
    },
  });

  const onSubmit = (data: z.infer<typeof areaSchema>) => {
    setLoading(true);
    console.log("Form submitted:", data);
    setTimeout(() => {
      setLoading(false);
      form.reset();
    }, 1000);
  };

  return (
    <div className="h-auto p-4">
      <Link
        href="/dashboard/settings/manage-departments"
        className="text-brand-primary text-sm mb-10"
      >
        &larr; Back
      </Link>

      <div className="mx-auto max-w-170">
        <h2 className="text-2xl font-semibold text-brand-primary-font mb-2">
          Set up new department
        </h2>
        <p className="text-muted-foreground text-sm mb-6">
          To support the continued growth and evolving needs of our
          organisation, we propose the establishment of a new [Department Name].
          This department will play a vital role in performing and managing
          tasks.
        </p>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input prefix="#" placeholder="e.g. marketing" {...field} />
                  </FormControl>
                  <p className="text-muted-foreground text-xs">
                    Names must be lowercase, must be without spaces or periods,
                    and can’t be longer than 80 characters.
                  </p>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Purpose */}
            <FormField
              control={form.control}
              name="purpose"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Purpose</FormLabel>
                  <FormControl>
                    <Input placeholder="What's the idea about?" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Send Invites */}
            <FormField
              control={form.control}
              name="invites"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Send invites to (optional)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Select up to 20 people to add to this area."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Instructions */}
            <FormField
              control={form.control}
              name="instructions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Instructions</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any setup instructions here..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Make Private */}
            <FormField
              control={form.control}
              name="isPrivate"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between">
                  <div>
                    <FormLabel className="flex items-center gap-2">
                      🔒 Make department private
                    </FormLabel>
                    <p className="text-muted-foreground text-xs">
                      This area can be joined by invite only.
                    </p>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Actions */}
            <div className="flex justify-end gap-4 pt-4">
              <Button type="button" variant="outline">
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-brand-primary text-white hover:bg-brand-secondary"
                disabled={loading}
              >
                {loading ? "Creating..." : "Create"}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default CreateDepartment;
