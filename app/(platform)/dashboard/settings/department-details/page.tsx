"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { <PERSON>, Bo<PERSON>, BookText } from "lucide-react";
import Members from "./_components/Members";
import Agents from "./_components/Agents";
import KnowledgeBase from "./_components/KnowledgeBase";

const DepartmentDetails = () => {
  const [tab, setTab] = useState("members");

  return (
    <div className="px-6 py-10 max-w-7xl mx-auto">
      <Link
        href="/dashboard/settings/manage-departments"
        className="text-brand-primary text-sm mb-10"
      >
        &larr; Back
      </Link>

      <h2 className="text-2xl font-semibold text-brand-primary-font mb-2 mt-10">
        # Marketing
      </h2>
      <p className="text-muted-foreground text-sm mb-6">
        To support the continued growth and evolving needs of our organization,
        we propose the establishment of a new [Department Name]. This department
        will play a vital role in performing and managing tasks.
      </p>

      <Tabs value={tab} onValueChange={setTab} className="w-full">
        <TabsList className="bg-transparent p-0 mb-4 border-b border-border">
          <TabsTrigger
            value="members"
            className="data-[state=active]:border-b-2 data-[state=active]:text-brand-primary border-brand-primary rounded-none text-sm px-4"
          >
            <Users className="w-4 h-4 mr-1" /> Members
          </TabsTrigger>
          <TabsTrigger
            value="agents"
            className="data-[state=active]:border-b-2 data-[state=active]:text-brand-primary border-brand-primary rounded-none text-sm px-4"
          >
            <Bot className="w-4 h-4 mr-1" /> Agents
          </TabsTrigger>
          <TabsTrigger
            value="knowledge"
            className="data-[state=active]:border-b-2 data-[state=active]:text-brand-primary border-brand-primary rounded-none text-sm px-4"
          >
            <BookText className="w-4 h-4 mr-1" /> Knowledge Base
          </TabsTrigger>
        </TabsList>

        <TabsContent value="members">
          <Members />
        </TabsContent>
        <TabsContent value="agents">
          <Agents />
        </TabsContent>
        <TabsContent value="knowledge">
          <KnowledgeBase />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DepartmentDetails;
