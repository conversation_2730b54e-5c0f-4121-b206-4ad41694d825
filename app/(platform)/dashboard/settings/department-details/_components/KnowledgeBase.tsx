"use client";

import { MoreHorizon<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const files = [
  {
    name: "Meeting_Notes_2025-05-09.docx",
    size: "242 mb",
    areas: ["# Design", "# Marketing"],
    date: "May 13, 2025",
    uploadedBy: "Arunima Ray",
  },
  {
    name: "Project_Plan_V2.pdf",
    size: "267 mb",
    areas: ["# Design", "# Marketing"],
    date: "May 13, 2025",
    uploadedBy: "Arunima Ray",
  },
  {
    name: "Invoice_#1234_March2025.xlsx",
    size: "267 mb",
    areas: ["# Design", "# Marketing"],
    date: "May 13, 2025",
    uploadedBy: "Arunima Ray",
  },
  {
    name: "Survey_results_Q1_2025.csv",
    size: "267 mb",
    areas: ["# Design", "# Marketing"],
    date: "May 13, 2025",
    uploadedBy: "Arunima Ray",
  },
  {
    name: "app_config.json",
    size: "267 mb",
    areas: ["# Design", "# Marketing"],
    date: "May 13, 2025",
    uploadedBy: "Arunima Ray",
  },
];

const KnowledgeBase = () => {
  return (
    <div className="mt-6">
      <h3 className="text-sm font-medium text-brand-primary-font mb-1">
        108 files in Rapid’s Knowledge Base
      </h3>
      <p className="text-sm text-muted-foreground mb-4">
        Collaborate, build, and deploy AI agents securely in one workspace.
      </p>

      <div className="w-full overflow-x-auto rounded-lg border border-border">
        <table className="min-w-full text-sm">
          <thead className="bg-muted/50 border-b border-border">
            <tr className="text-left">
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                Name
              </th>
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                File Size
              </th>
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                Areas
              </th>
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                Date Created
              </th>
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                Uploaded By
              </th>
              <th className="px-4 py-3"></th>
            </tr>
          </thead>
          <tbody>
            {files.map((file, i) => (
              <tr key={i} className="border-b border-border hover:bg-muted/30">
                <td className="px-4 py-3 text-brand-primary-font font-medium">
                  {file.name}
                </td>
                <td className="px-4 py-3">{file.size}</td>
                <td className="px-4 py-3">
                  <div className="flex gap-2 flex-wrap">
                    {file.areas.map((area, j) => (
                      <span
                        key={j}
                        className={
                          area.includes("Marketing")
                            ? "text-green-700 bg-green-100 px-2 py-1 text-xs rounded-full"
                            : "text-purple-700 bg-purple-100 px-2 py-1 text-xs rounded-full"
                        }
                      >
                        {area}
                      </span>
                    ))}
                  </div>
                </td>
                <td className="px-4 py-3">{file.date}</td>
                <td className="px-4 py-3 font-semibold text-brand-primary-font">
                  {file.uploadedBy}
                </td>
                <td className="px-4 py-3 text-right">
                  <Button size="icon" variant="ghost">
                    <MoreHorizontal className="w-4 h-4 text-muted-foreground" />
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default KnowledgeBase;
