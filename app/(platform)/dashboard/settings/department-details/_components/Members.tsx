"use client";

import Image from "next/image";
import { MoreHorizontal } from "lucide-react";

import { Button } from "@/components/ui/button";

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const members = Array.from({ length: 8 }, (_, i) => ({
  name: "<PERSON><PERSON>",
  username: "@natali",
  avatar: "/avatars/01.png",
  accessTo: "<PERSON>, the blog agent",
  role: "Editor",
  status: "Active",
}));

const Members = () => {
  return (
    <div className="mt-6">
      <h3 className="text-sm font-medium text-brand-primary-font mb-1">
        72 people in this department
      </h3>
      <p className="text-sm text-muted-foreground mb-4">
        Discover design files, templates, plugins, and widgets from creators
        around the world—a great place for inspiration and shortcuts to boost
        your workflow.
      </p>

      <div className="w-full overflow-x-auto rounded-lg border border-border">
        <table className="min-w-full text-sm">
          <thead className="bg-muted/50 border-b border-border">
            <tr className="text-left">
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                Name
              </th>
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                Access To
              </th>
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                Role
              </th>
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                Status
              </th>
              <th className="px-4 py-3"></th>
            </tr>
          </thead>
          <tbody>
            {members.map((member, i) => (
              <tr key={i} className="border-b border-border hover:bg-muted/30">
                <td className="px-4 py-3 flex items-center gap-3">
                  <Image
                    src="/assets/avatars/avatar-4.svg"
                    alt="avatar"
                    width={40}
                    height={40}
                  />
                  <div>
                    <div className="font-medium text-brand-primary-font">
                      {member.name}
                    </div>
                    <div className="text-muted-foreground text-xs">
                      {member.username}
                    </div>
                  </div>
                </td>
                <td className="px-4 py-3 text-sm">
                  <span className="inline-flex items-center bg-blue-100 text-blue-600 text-xs font-medium rounded-full px-2 py-1">
                    {member.accessTo}
                  </span>
                  <span className="ml-2 text-xs text-muted font-medium bg-muted px-2 py-1 rounded-full">
                    +2
                  </span>
                </td>
                <td className="px-4 py-3 text-sm">{member.role}</td>
                <td className="px-4 py-3 text-sm">
                  <span className="inline-flex items-center text-green-600 text-xs font-medium">
                    <span className="w-2 h-2 mr-1 rounded-full bg-green-500"></span>
                    {member.status}
                  </span>
                </td>
                <td className="px-4 py-3 text-right">
                  <Button size="icon" variant="ghost">
                    <MoreHorizontal className="w-4 h-4 text-muted-foreground" />
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Members;
