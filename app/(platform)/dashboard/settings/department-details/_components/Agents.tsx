"use client";

import Image from "next/image";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";

const agents = Array.from({ length: 3 }, () => ({
  name: "<PERSON>, the marketing agent",
  description:
    "Collaborate, build, and deploy AI agents securely in one workspace.",
  avatar: "/avatars/01.png",
  tags: ["# Design", "Public"],
}));

const Agents = () => {
  return (
    <div className="mt-6">
      <h3 className="text-sm font-medium text-brand-primary-font mb-1">
        3 agents
      </h3>
      <p className="text-sm text-muted-foreground mb-4">
        Collaborate, build, and deploy AI agents securely in one workspace.
      </p>

      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
        {agents.map((agent, i) => (
          <div
            key={i}
            className="rounded-xl border border-border bg-card p-4 shadow-sm hover:bg-brand-card-hover transition-colors relative"
          >
            <div className="flex items-start justify-between">
              <Image
                src="/assets/avatars/avatar-4.svg"
                alt="avatar"
                width={40}
                height={40}
              />
              <Button size="icon" variant="ghost">
                <MoreHorizontal className="w-4 h-4 text-muted-foreground" />
              </Button>
            </div>

            <div className="flex gap-2 mt-4 text-xs font-medium">
              <span className="text-purple-700 bg-purple-100 px-2 py-1 rounded-full">
                {agent.tags[0]}
              </span>
              <span className="text-green-700 bg-green-100 px-2 py-1 rounded-full">
                {agent.tags[1]}
              </span>
            </div>

            <div className="mt-2 font-semibold text-brand-primary-font text-sm">
              {agent.name}
            </div>
            <div className="text-muted-foreground text-xs mt-1">
              {agent.description}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Agents;
