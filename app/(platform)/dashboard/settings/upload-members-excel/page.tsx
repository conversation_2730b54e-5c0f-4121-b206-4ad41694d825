"use client";

import React, { useState, useRef } from "react";
import { Upload, FileUp, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { PrimaryButton } from "@/components/shared/PrimaryButton";

export const UploadMemberExcel = () => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) setSelectedFile(file);
  };

  const handleUpload = () => {
    if (!selectedFile) return;
    console.log("Uploading file:", selectedFile.name);
  };

  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="px-6 py-10 max-w-7xl mx-auto space-y-6">
      {/* Back Link */}
      <Link
        href="/dashboard/settings/manage-members"
        className="text-sm text-brand-primary flex items-center gap-1 hover:underline"
      >
        <ArrowLeft size={16} />
        Back
      </Link>

      {/* Heading */}
      <div className="space-y-1">
        <h1 className="text-2xl font-semibold text-brand-primary">
          Upload Excel
        </h1>
        <p className="text-sm text-muted-foreground">
          Sure! Here are the single-step instructions to upload an Excel sheet
          on Ruh–
        </p>
      </div>

      {/* Grid Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mt-6">
        {/* Left Column */}
        <div className="text-sm leading-6 space-y-6">
          {/* Steps */}
          <ol className="list-decimal list-inside space-y-2 text-foreground">
            <li>
              Go to Global Settings &gt; Data Import or Org &gt; Dashboard &gt;
              Import Employee Job Details.
            </li>
            <li>
              Download the Excel template by clicking “Download Excel Template”.
            </li>
            <li>
              Fill in the Excel file with required data (email, role,
              department, message).
            </li>
            <li>
              Go back to Ruh and click “Upload Excel File” to select your
              updated file.
            </li>
            <li>
              Match the columns in your file with Ruh’s system fields when
              prompted.
            </li>
            <li>Review the data preview, correct any errors if shown.</li>
            <li>Click “Complete” to finalize the import.</li>
          </ol>

          {/* Action Buttons */}
          <div className="flex gap-4 flex-wrap pt-2">
            {/* Download Template Button */}
            <a
              href="/templates/member-upload-template.xlsx"
              download
              className="w-fit"
            >
              <Button
                variant="outline"
                className="text-sm rounded-md border border-brand-primary text-brand-primary hover:bg-brand-clicked px-4"
              >
                <FileUp className="w-4 h-4 mr-2" />
                Download Excel Template
              </Button>
            </a>

            {/* File Input */}
            <Input
              ref={fileInputRef}
              id="excel-upload"
              type="file"
              accept=".xlsx,.xls,.csv"
              className="hidden"
              onChange={handleFileChange}
            />

            {/* Upload Button */}
            <PrimaryButton
              onClick={triggerFileSelect}
              className="w-fit text-sm rounded-md px-4"
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload Excel
            </PrimaryButton>
          </div>

          {/* File Name */}
          {selectedFile && (
            <div className="text-sm text-muted-foreground mt-2">
              Selected file:{" "}
              <span className="font-medium text-foreground">
                {selectedFile.name}
              </span>
            </div>
          )}

          {/* Final Upload Button */}
          {selectedFile && (
            <Button
              className="mt-4 bg-brand-primary text-white hover:bg-brand-secondary text-sm rounded-md"
              onClick={handleUpload}
            >
              Upload & Process
            </Button>
          )}
        </div>

        {/* Right Column: Read Instructions */}
        <div className="text-sm space-y-3 md:pt-2">
          <h2 className="text-lg font-semibold text-brand-primary">
            Read Instructions
          </h2>
          <ul className="list-disc list-inside text-muted-foreground space-y-1">
            <li>
              Always start with the official Keka template to avoid format
              issues.
            </li>
            <li>Do not modify or remove column headers.</li>
            <li>Fill in all required fields.</li>
            <li>Mandatory: Email, Role, Department, Message.</li>
            <li>Use exact dropdown values (case-sensitive).</li>
            <li>Ensure no special characters unless required.</li>
            <li>Ensure correct date format (DD/MM/YYYY or YYYY-MM-DD).</li>
            <li>No extra spaces before/after text.</li>
            <li>
              Numeric fields (e.g. salary) must contain valid numbers only.
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default UploadMemberExcel;
