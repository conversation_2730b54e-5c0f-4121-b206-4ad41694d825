"use client";

import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { MoreHorizontal } from "lucide-react";

const agents = Array.from({ length: 6 }, () => ({
  name: "<PERSON>, the marketing agent",
  description:
    "Collaborate, build, and deploy AI agents securely in one workspace.",
  avatar: "/avatars/01.png",
  tags: ["# Design", "Public"],
}));

const AgentCard = ({ name, description, tags }: any) => (
  <Link href="/dashboard/settings/agent-details">
    <div className="rounded-xl border border-border bg-card p-4 shadow-sm hover:bg-brand-card-hover transition-colors relative">
      <div className="flex items-start justify-between">
        <Image
          src="/assets/avatars/avatar-4.svg"
          alt="avatar"
          width={40}
          height={40}
        />
        <Button size="icon" variant="ghost">
          <MoreHorizontal className="w-4 h-4 text-muted-foreground" />
        </Button>
      </div>
      <div className="flex gap-2 mt-4 text-xs font-medium">
        <span className="text-purple-700 bg-purple-100 px-2 py-1 rounded-full">
          {tags[0]}
        </span>
        <span className="text-green-700 bg-green-100 px-2 py-1 rounded-full">
          {tags[1]}
        </span>
      </div>
      <div className="mt-2 font-semibold text-brand-primary-font text-sm">
        {name}
      </div>
      <div className="text-muted-foreground text-xs mt-1">{description}</div>
    </div>
  </Link>
);

const ManageAgents = () => {
  return (
    <div className="p-6 space-y-10">
      <div>
        <h2 className="text-lg font-semibold text-brand-primary-font">
          Config’s newest arrivals
        </h2>
        <p className="text-sm text-muted-foreground">
          Collaborate, build, and deploy AI agents securely in one workspace.
        </p>

        <div className="w-[80%] mt-4 rounded-xl overflow-hidden relative h-48 md:h-56">
          <Image
            src="/assets/misc/ad.png"
            alt="Ad banner"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 flex items-center px-8 py-6 bg-black/10 rounded-xl">
            <div>
              <h3 className="text-white text-lg md:text-xl font-semibold mb-2">
                Have a project idea in mind.
                <br />
                Get in touch and let’s chat!
              </h3>
             
            </div>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-sm font-medium text-brand-primary-font mb-1">
          Your recent agents
        </h3>
        <p className="text-sm text-muted-foreground mb-4">
          Collaborate, build, and deploy AI agents securely in one workspace.
        </p>
        <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
          {agents.slice(0, 3).map((agent, i) => (
            <AgentCard key={i} {...agent} />
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-sm font-medium text-brand-primary-font mb-1">
          72 agents in Rapid Innovation
        </h3>
        <p className="text-sm text-muted-foreground mb-4">
          Collaborate, build, and deploy AI agents securely in one workspace.
        </p>
        <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
          {agents.map((agent, i) => (
            <AgentCard key={i} {...agent} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ManageAgents;
