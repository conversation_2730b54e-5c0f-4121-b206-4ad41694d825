"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Users, BookOpen, Tags } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import clsx from "clsx";

const menu = [
  { name: "Workspace Settings", icon: Tags, path: "/" },
  {
    name: "Manage Departments",
    icon: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    path: "/manage-departments",
  },
  { name: "Manage Agents", icon: Users, path: "/manage-agents" },
  { name: "Manage Members", icon: Users, path: "/manage-members" },
  { name: "Knowledge Base", icon: BookOpen, path: "/knowledge-base" },
];

const Sidebar = () => {
  const pathname = usePathname();

  return (
    <aside className="w-60 shadow-inner border-r h-full flex flex-col">
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold">Rapid Innovation</h2>
        <p className="text-sm text-gray-500">Account</p>
      </div>
      <nav className="flex-1 overflow-y-auto">
        <ul className="mt-4 space-y-1 px-2">
          {menu.map(({ name, icon: Icon, path }) => (
            <li key={name}>
              <Link
                href={path}
                className={clsx(
                  "flex items-center gap-3 px-4 py-2 rounded-md text-sm font-medium",
                  pathname === path
                    ? "bg-purple-100 text-purple-600"
                    : "text-brand-primary-font hover:bg-purple-100 dark:hover:bg-gray-700"
                )}
              >
                <Icon className="w-5 h-5" />
                {name}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
};
export default Sidebar;
