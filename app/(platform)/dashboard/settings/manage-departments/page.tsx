"use client";

import Link from "next/link";
import Image from "next/image";
import { Lock, Hash, Users2, <PERSON><PERSON>ex<PERSON>, Bot } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

const workspaceAreas = [
  {
    title: "Human Resource",
    description:
      "Collaborate, build, and deploy AI agents securely in one workspace.",
    icon: <Hash className="w-4 h-4" />, // public
    author: "<PERSON><PERSON><PERSON><PERSON>",
    avatar: "/avatars/01.png",
    agents: 2,
    members: 129,
    files: 36,
    isPrivate: false,
  },
  {
    title: "Finance",
    description:
      "Collaborate, build, and deploy AI agents securely in one workspace.",
    icon: <Lock className="w-4 h-4" />, // private
    author: "<PERSON><PERSON><PERSON><PERSON>",
    avatar: "/avatars/02.png",
    agents: 2,
    members: 129,
    files: 36,
    isPrivate: true,
  },
  {
    title: "Human Resource",
    description:
      "Collaborate, build, and deploy AI agents securely in one workspace.",
    icon: <Hash className="w-4 h-4" />, // public
    author: "<PERSON><PERSON><PERSON><PERSON>",
    avatar: "/avatars/03.png",
    agents: 2,
    members: 129,
    files: 36,
    isPrivate: false,
  },
];

const ManageDepartment = () => {
  return (
    <div className="px-6 py-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-semibold text-brand-primary-font">
            Discover your departments
          </h2>
          <p className="text-muted-foreground text-sm">
            Collaborate, build, and deploy AI agents securely in one workspace.
          </p>
        </div>
        <Link href="/dashboard/settings/create-department">
          <Button className="bg-brand-primary text-white hover:bg-brand-secondary text-sm">
            + Add Department
          </Button>
        </Link>
      </div>

      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
        {workspaceAreas.map((area, i) => (
          <Link href="/dashboard/settings/department-details" key={i}>
            <div
              key={i}
              className="rounded-xl border border-border bg-card p-4 shadow-sm hover:bg-brand-card-hover transition-colors cursor-pointer"
            >
              <div className="flex items-center gap-2 font-semibold text-brand-primary-font mb-1">
                {area.icon}
                {area.title}
              </div>
              <p className="text-sm text-muted-foreground mb-4">
                {area.description}
              </p>
              <div className="flex items-center gap-2 text-sm text-brand-secondary-font">
                <Image
                  src="/assets/avatars/avatar-4.svg"
                  alt="avatar"
                  width={40}
                  height={40}
                />
                <span className="mr-auto">{area.author}</span>
                <div className="flex items-center gap-1">
                  <Bot className="w-4 h-4" />{" "}
                  {area.agents.toString().padStart(2, "0")}
                </div>
                <div className="flex items-center gap-1">
                  <Users2 className="w-4 h-4" /> {area.members}
                </div>
                <div className="flex items-center gap-1">
                  <FileText className="w-4 h-4" /> {area.files}
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default ManageDepartment;
