"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, MoreHorizontal } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const AgentDetails = () => {
  return (
    <div className="p-4 space-y-6">
      <Link
        href="/dashboard/settings/manage-agents"
        className="text-sm text-brand-primary font-semibold flex items-center gap-1"
      >
        <ArrowLeft className="w-4 h-4" /> Back
      </Link>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Left Section */}
        <div className="space-y-4">
          <div className="bg-card rounded-xl p-4 border border-border">
            <div className="flex items-start justify-between">
              <Image
                src="/assets/avatars/avatar-4.svg"
                alt="avatar"
                width={40}
                height={40}
                className="rounded-full"
              />
              <Button size="icon" variant="ghost">
                <MoreHorizontal className="w-4 h-4 text-muted-foreground" />
              </Button>
            </div>
            <div className="flex gap-2 mt-4 text-xs font-medium">
              <span className="text-purple-700 bg-purple-100 px-2 py-1 rounded-full">
                # Design
              </span>
              <span className="text-green-700 bg-green-100 px-2 py-1 rounded-full">
                Public
              </span>
            </div>
            <h3 className="mt-2 font-semibold text-brand-primary-font text-sm">
              Julia, the marketing agent
            </h3>
            <p className="text-muted-foreground text-xs mt-1">
              Collaborate, build, and deploy AI agents securely in one
              workspace.
            </p>
          </div>

          <div className="bg-card rounded-xl p-4 border border-border">
            <h4 className="text-sm font-semibold mb-1">Language Model</h4>
            <p className="text-muted-foreground text-xs mb-2">
              Collaborate, build, and deploy AI agents securely in one
              workspace.
            </p>
            <div className="flex items-center border border-border p-3 rounded-md text-sm">
              <span className="text-muted-foreground">GPT 4 32k 0513</span>
            </div>
          </div>

          <div className="bg-card rounded-xl p-4 border border-border">
            <h4 className="text-sm font-semibold mb-1">Connected Areas (2)</h4>
            <p className="text-muted-foreground text-xs mb-2">
              Collaborate, build, and deploy AI agents securely in one
              workspace.
            </p>
            <div className="space-y-3">
              <div>
                <div className="text-sm font-medium"># Human Resource</div>
                <div className="flex items-center space-x-1 mt-1">
                  <Image
                    src="/assets/avatars/avatar-4.svg"
                    alt="avatar"
                    width={24}
                    height={24}
                    className="rounded-full"
                  />
                  <Image
                    src="/assets/avatars/avatar-4.svg"
                    alt="avatar"
                    width={24}
                    height={24}
                    className="rounded-full"
                  />
                  <span className="text-muted-foreground text-xs">+2</span>
                </div>
              </div>
              <div>
                <div className="text-sm font-medium">Finance</div>
                <div className="flex items-center space-x-1 mt-1">
                  <Image
                    src="/assets/avatars/avatar-4.svg"
                    alt="avatar"
                    width={24}
                    height={24}
                    className="rounded-full"
                  />
                  <Image
                    src="/assets/avatars/avatar-4.svg"
                    alt="avatar"
                    width={24}
                    height={24}
                    className="rounded-full"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Section */}
        <div className="md:col-span-2 space-y-6">
          <div className="bg-card rounded-xl p-4 border border-border">
            <h4 className="text-base font-semibold mb-1">
              How your AI should work?
            </h4>
            <p className="text-muted-foreground text-sm mb-2">
              Collaborate, build, and deploy AI agents securely in one
              workspace.
            </p>
            <div className="bg-muted p-3 rounded-md text-sm">
              A finance agent AI builder should analyze financial data, automate
              tasks like budgeting, forecasting, and expense tracking, provide
              insights, detect anomalies, and ensure data security. It must
              integrate with financial tools, support user queries, adapt to
              individual goals, and comply with regulations while delivering
              accurate, real-time, and personalized financial guidance.
            </div>
            <div className="mt-6">
              <h5 className="text-sm font-medium mb-2">Workflow Builder</h5>
              <p className="text-muted-foreground text-sm mb-2">
                Collaborate, build, and deploy AI agents securely in one
                workspace.
              </p>
              <div className="bg-muted p-3 rounded-md flex items-center justify-between">
                <span className="text-muted-foreground text-sm">
                  Review Case Preview
                </span>
                <Button className="bg-brand-primary hover:bg-brand-secondary text-white text-sm">
                  Edit in workflow builder
                </Button>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-xl p-4 border border-border">
            <h4 className="text-base font-semibold mb-1">
              What your AI worker can do?
            </h4>
            <p className="text-muted-foreground text-sm mb-4">
              Collaborate, build, and deploy AI agents securely in one
              workspace.
            </p>
            <div className="flex items-center justify-between bg-muted p-3 rounded-md">
              <div className="text-sm">
                <div className="font-medium">Zapier</div>
                <p className="text-muted-foreground text-xs">
                  Build custom automations and integrations with other apps.
                </p>
              </div>
              <Button className="text-sm" variant="outline">
                Edit Tool
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentDetails;
