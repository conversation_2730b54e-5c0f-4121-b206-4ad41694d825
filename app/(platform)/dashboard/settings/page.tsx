"use client";

import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { MoreHorizontal } from "lucide-react";

const admins = [
  {
    name: "Arun<PERSON> Ray",
    email: "<EMAIL>",
    role: "Primary Owner",
    joined: "May 9, 2025",
    avatar: "/avatars/01.png",
  },
  {
    name: "<PERSON><PERSON><PERSON> <PERSON>",
    email: "<EMAIL>",
    role: "Owner",
    joined: "May 9, 2025",
    avatar: "/avatars/01.png",
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    joined: "May 9, 2025",
    avatar: "/avatars/01.png",
  },
];

const WorkspaceSettings = () => {
  return (
    <div className="p-4 space-y-6">
      {/* Banner */}
      <div className="w-full overflow-hidden rounded-xl">
        <div className="relative w-full h-56">
          <Image
            src="/assets/misc/ad.png"
            alt="Workspace Banner"
            fill
            className="object-cover w-[75%]"
          />
          <div className="absolute inset-0 bg-black/10 flex items-center px-8">
            <div>
              <h3 className="text-white text-xl font-semibold leading-snug">
                Have a project idea in mind.
                <br />
                Get in touch and let&apos;s chat!
              </h3>
            </div>
          </div>
        </div>
      </div>

      {/* Workspace Info */}
      <div>
        <h2 className="text-lg font-semibold text-brand-primary-font">
          About This Workspace
        </h2>
        <p className="text-sm text-muted-foreground mb-4">
          To support the continued growth and evolving needs of our
          organization, we propose the establishment of a new [Department Name].
          This department will play a vital role in performing and managing
          tasks.
        </p>

        <Tabs defaultValue="overview">
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="admins">Admins & Owners</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="border border-border rounded-xl bg-card">
              <div className="flex items-center justify-between border-b border-border p-6">
                <div className="flex items-center gap-4">
                  <Image
                    src="/assets/logos/rapid-logo.svg"
                    alt="Rapid Innovation"
                    width={48}
                    height={48}
                  />
                  <div>
                    <div className="text-brand-primary-font font-semibold">
                      Rapid Innovation
                    </div>
                    <div className="text-muted-foreground text-sm">
                      www.rapidinnovation.dev
                    </div>
                  </div>
                </div>
                <Button variant="outline">↗ Edit Details</Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6 text-sm">
                <div>
                  <div className="text-muted-foreground">Plan Type</div>
                  <div className="text-brand-primary-font font-medium">PRO</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Date Created</div>
                  <div className="text-brand-primary-font font-medium">
                    May 9, 2025
                  </div>
                </div>
                <div className="md:col-span-2">
                  <div className="text-muted-foreground">Terms of Service</div>
                  <div className="text-brand-primary text-sm font-medium cursor-pointer">
                    Review
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="admins">
            <div className="border border-border rounded-xl p-6 bg-card space-y-4">
              <div className="flex items-center gap-4">
                <Image
                  src="/assets/logos/rapid-logo.svg"
                  alt="Rapid Innovation"
                  width={48}
                  height={48}
                />
                <div>
                  <div className="text-brand-primary-font font-semibold">
                    Rapid Innovation
                  </div>
                  <div className="text-muted-foreground text-sm">
                    rapidinnovation.ruh.com
                  </div>
                </div>
              </div>
              <div className="divide-y divide-border">
                {admins.map((admin, i) => (
                  <div
                    key={i}
                    className="flex items-center justify-between py-4"
                  >
                    <div className="flex items-center gap-4">
                      <Image
                        src="/assets/avatars/avatar-4.svg"
                        alt="avatar"
                        width={40}
                        height={40}
                        className="w-10 h-10 rounded-full"
                      />
                      <div>
                        <div className="text-brand-primary-font font-medium text-sm">
                          {admin.name}
                        </div>
                        <div className="text-muted-foreground text-xs">
                          {admin.email}
                        </div>
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground hidden md:block">
                      Joined on {admin.joined}
                    </div>
                    <div className="text-sm text-brand-primary-font w-28 text-right hidden md:block">
                      {admin.role}
                    </div>
                    <Button size="icon" variant="ghost">
                      <MoreHorizontal className="w-4 h-4 text-muted-foreground" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default WorkspaceSettings;
