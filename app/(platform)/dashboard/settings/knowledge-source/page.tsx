"use client";

import { useState, useRef } from "react";
import Image from "next/image";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { PrimaryButton } from "@/components/shared/PrimaryButton";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  department: z.string().min(1, "Department is required"),
  files: z.any().optional(),
});

const KnowledgeSourceForm = () => {
  const [fileList, setFileList] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      department: "",
      files: undefined,
    },
  });

  const handleFilesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      const updatedFiles = [...fileList, ...newFiles];
      setFileList(updatedFiles);
      form.setValue("files", updatedFiles);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleFileRemove = (index: number) => {
    const updatedList = [...fileList];
    updatedList.splice(index, 1);
    setFileList(updatedList);
    form.setValue("files", updatedList);
  };

  const onSubmit = (data: any) => {
    console.log({ ...data, files: fileList });
  };
  return (
    <div className="p-4">
      <Link
        href="/dashboard/settings/knowledge-base"
        className="text-sm text-brand-primary"
      >
        ← Back
      </Link>
      <div className="max-w-xl mx-auto px-4 py-8 space-y-6">
        <div>
          <h2 className="text-xl font-semibold text-brand-primary-font">
            Knowledge Source
          </h2>

          <p className="text-sm text-brand-secondary-font mt-1">
            To support the continued growth and evolving needs of our
            organization, we propose the establishment of a new [Department
            Name]. This department will play a vital role in performing and
            managing tasks.
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem className="mb-8">
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Placeholder text" {...field} />
                  </FormControl>
                  <FormDescription>
                    To add files from your computer, click upload and select
                    files.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="department"
              render={({ field }) => (
                <FormItem className="mb-8">
                  <FormLabel>Department</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a department" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="sales">Sales</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="it">IT</SelectItem>
                      <SelectItem value="hr">HR</SelectItem>
                      <SelectItem value="operations">Operations</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Select up to 20 people to add to this department.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormItem>
              <FormLabel>Documents from your computer</FormLabel>
              <FormControl>
                <Input
                  type="file"
                  multiple
                  onChange={handleFilesChange}
                  ref={fileInputRef}
                  placeholder="Choose file"
                />
              </FormControl>
              <FormDescription>
                To add files from your computer, click upload and select files.{" "}
                <span className="underline cursor-pointer">Upload</span>
              </FormDescription>
              <ul className="mt-2 space-y-1">
                {fileList.map((file, index) => (
                  <li
                    key={index}
                    className="flex justify-between items-center text-sm bg-muted px-3 py-2 rounded"
                  >
                    <span>{file.name}</span>
                    <button
                      type="button"
                      onClick={() => handleFileRemove(index)}
                      className="text-red-500 text-xs"
                    >
                      Remove
                    </button>
                  </li>
                ))}
              </ul>
            </FormItem>

            <div>
              <FormLabel>Data Sources</FormLabel>
              <div className="space-y-3 mt-2">
                <div className="flex items-center gap-3 border rounded-lg p-3 bg-brand-clicked">
                  <Image
                    src="/assets/icons/GoogleDriveIcon.svg"
                    alt="Google Drive"
                    width={24}
                    height={24}
                  />
                  <div>
                    <p className="font-medium text-sm text-brand-primary-font">
                      Google Drive
                    </p>
                    <p className="text-sm text-brand-secondary-font">
                      Build custom automations.
                    </p>
                  </div>
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                You can upload documents either from your computer or connect
                external sources like Google Drive.
              </p>
            </div>

            <div className="flex gap-3 pt-4">
              <Button type="button" variant="outline">
                Cancel
              </Button>
              <PrimaryButton
                type="submit"
                className="w-fit bg-brand-primary text-white hover:opacity-90"
              >
                Upload
              </PrimaryButton>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default KnowledgeSourceForm;
