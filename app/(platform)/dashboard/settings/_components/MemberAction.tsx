import React from "react";

import { MoreHorizontal } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Props {
  activeTab: string;
  handleAction: (action: string, memberId: unknown) => void;
  member: any;
}

const MemberAction = ({ activeTab, member, handleAction }: Props) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-8 w-8 p-0 data-[state=open]:bg-gray-100"
        >
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {activeTab === "active" ? (
          <>
            <DropdownMenuItem
              onClick={() => handleAction("change-role", member.id)}
            >
              Change role type
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => handleAction("deactivate", member.id)}
              className="text-red-600 focus:bg-red-100"
            >
              Deactivate account
            </DropdownMenuItem>
          </>
        ) : (
          <DropdownMenuItem
            onClick={() => handleAction("cancel-invite", member.id)}
            className="text-red-600 focus:bg-red-100"
          >
            Cancel Invitation
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default MemberAction;
