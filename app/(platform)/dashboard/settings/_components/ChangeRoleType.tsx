"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  Di<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface RoleTypeModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (role: RoleType) => void;
  defaultRole?: RoleType;
}

type RoleType = "Workspace Admin" | "Editor" | "Regular Member";

const ROLE_OPTIONS: { label: RoleType; description: string }[] = [
  {
    label: "Workspace Admin",
    description: "Access to manage users, permissions, and settings",
  },
  {
    label: "Editor",
    description: "Access to manage users, permissions, and settings",
  },
  {
    label: "Regular Member",
    description: "Access to manage users, permissions, and settings",
  },
];

const ChangeRoleType = ({
  open,
  onClose,
  onSave,
  defaultRole = "Workspace Admin",
}: RoleTypeModalProps) => {
  const [selectedRole, setSelectedRole] = useState<RoleType>(defaultRole);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[476px] rounded-xl">
        <DialogHeader>
          <DialogTitle className="text-[20px] text-primary-font font-bold">
            Change role type
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            The following users have access to this agent.
          </p>
        </DialogHeader>

        <RadioGroup
          value={selectedRole}
          onValueChange={(val: any) => setSelectedRole(val as RoleType)}
          className="space-y-2"
        >
          {ROLE_OPTIONS.map((role) => (
            <label
              key={role.label}
              htmlFor={role.label}
              className={`flex items-start gap-3 p-4 border rounded-lg cursor-pointer transition-all ${
                selectedRole === role.label
                  ? "border-brand-primary bg-brand-clicked"
                  : "border-border bg-transparent"
              }`}
            >
              <RadioGroupItem
                id={role.label}
                value={role.label}
                className="mt-1 data-[state=checked]:border-brand-primary"
              />
              <div className="space-y-1">
                <div className="font-medium">{role.label}</div>
                <div className="text-sm text-muted-foreground">
                  {role.description}
                </div>
              </div>
            </label>
          ))}
        </RadioGroup>

        <DialogFooter className="w-full mt-4 flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <PrimaryButton
            className="w-30 bg-brand-primary text-brand-white-text hover:opacity-90"
            onClick={() => onSave(selectedRole)}
          >
            Save
          </PrimaryButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ChangeRoleType;
