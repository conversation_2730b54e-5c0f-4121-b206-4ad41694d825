"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { ListFilter } from "lucide-react";

import { ACTIVE_MEMBERS, INVITED_MEMBERS } from "./memberData";
import { Button } from "@/components/ui/button";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import MemberAction from "./MemberAction";
import ChangeRoleType from "./ChangeRoleType";

const MemberList = () => {
  const [activeTab, setActiveTab] = useState("active");
  const [openRoleTypeModal, setOpenRoleTypeModal] = useState(false);

  const memberDataList =
    activeTab === "active" ? ACTIVE_MEMBERS : INVITED_MEMBERS;

  const handleAction = (action: string, memberId: any) => {
    if (action === "change-role") {
      setOpenRoleTypeModal(true);
    }
    console.log(`Action "${action}" triggered for member ID: ${memberId}`);
  };

  const handleRoleChange = () => {
    console.log("set role type");
  };

  return (
    <>
      <div className="container mx-auto p-6">
        <div className="flex flex-col sm:flex-row items-center justify-between mb-6">
          <h1 className="text-2xl font-semibold  mb-4 sm:mb-0">
            Manage Members
          </h1>
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <Link href="/dashboard/settings/upload-members-excel">
              <Button variant="outline">Import Members in Bulk</Button>
            </Link>

            <Link href="/dashboard/settings/invite-member">
              <PrimaryButton>Invite Members</PrimaryButton>
            </Link>
          </div>
        </div>

        <div className="flex items-center justify-between mb-6 border-b border-border">
          <div className="flex space-x-4">
            <button
              className={`py-2 px-1 text-sm font-medium ${
                activeTab === "active"
                  ? "border-b-2 border-brand-primary text-brand-primary"
                  : "text-primary-font hover:text-brand-secondary"
              }`}
              onClick={() => setActiveTab("active")}
            >
              Active
            </button>
            <button
              className={`py-2 px-1 text-sm font-medium ${
                activeTab === "invited"
                  ? "border-b-2 border-brand-primary text-brand-primary"
                  : "text-primary-font hover:text-brand-secondary"
              }`}
              onClick={() => setActiveTab("invited")}
            >
              Invited
            </button>
          </div>

          <Button variant="outline" size="sm">
            <ListFilter className="w-5 h-5" />
            Filter
          </Button>
        </div>

        <div className="shadow overflow-hidden border-b border-border sm:rounded-lg">
          <table className="min-w-full divide-y divide-border bg-card text-foreground">
            <thead className="bg-muted text-muted-foreground">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                >
                  Name
                </th>

                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                >
                  {activeTab === "active" ? "Areas" : "Teams"}
                </th>

                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                >
                  Role
                </th>

                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                >
                  Status
                </th>

                <th scope="col" className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>

            <tbody className="bg-card divide-y divide-border">
              {memberDataList.map((member) => (
                <tr key={member.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <Image
                          src="/assets/avatars/avatar-4.svg"
                          alt="avatar"
                          width={40}
                          height={40}
                        />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-foreground">
                          {member.name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {member.handle}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-wrap gap-1">
                      {member.areas.map((area, index) => (
                        <span
                          key={index}
                          className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-muted text-muted-foreground"
                        >
                          {area}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                    {member.role}
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                      {member.status}
                    </span>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <MemberAction
                      activeTab={activeTab}
                      handleAction={handleAction}
                      member={member}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {memberDataList.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No members found for this tab.
            </div>
          )}
        </div>
      </div>

      <ChangeRoleType
        open={openRoleTypeModal}
        onClose={() => setOpenRoleTypeModal(false)}
        onSave={handleRoleChange}
      />
    </>
  );
};

export default MemberList;
