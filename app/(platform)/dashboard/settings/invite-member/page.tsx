"use client";

import { useState } from "react";
import Link from "next/link";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import api from "@/services/axios";

import { useOrgStore } from "@/hooks/use-organization";

const formSchema = z.object({
  sendInvitesTo: z
    .string()
    .email("Please enter a valid email address.")
    .min(1, {
      message: "Please enter at least one recipient.",
    }),
  department: z.string().min(1, {
    message: "Please specify the department.",
  }),
  inviteAs: z.string().min(1, {
    message: "Please specify a role.",
  }),
  permission: z.string().min(1, {
    message: "Please specify a permission.",
  }),
});

const InviteMemberForm = () => {
  const { organization } = useOrgStore();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      sendInvitesTo: "",
      department: "",
      inviteAs: "",
      permission: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    console.log("clicked=");
    console.log("organization=", organization);
    try {
      setIsLoading(true);

      if (organization && organization?.length > 0) {
        const data = {
          organisationId: organization[0]?.organisation?.id,
          email: values.sendInvitesTo,
          department: values.department,
          role: values.inviteAs,
          permission: values.permission,
        };

        await api.post("/organisations/invite", data);
        toast.success("Invitation sent successfully!!");
        form.reset();
      }
    } catch (error) {
      toast.error("Failed to send invite");
      console.log("error=>", error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="h-auto p-4">
      <Link
        href="/dashboard/settings/manage-members"
        className="text-sm text-brand-primary font-semibold"
      >
        &larr; Back to Members
      </Link>

      <div className="mx-auto max-w-170">
        <div className="p-6">
          <h1 className="text-2xl font-semibold text-primary-font mb-4">
            # Invite people to Rapid Innovation
          </h1>
          <p className="text-primary-font mb-6">
            To support the continued growth and evolving needs of our
            organization, we propose the establishment of a new [Department
            Name]. This department will play a vital role in performing and
            managing tasks.
          </p>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="sendInvitesTo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Send invites to</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="<EMAIL>"
                        type="email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger>
                          <span>{field.value || "Select department"}</span>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="General">General</SelectItem>
                          <SelectItem value="HR">HR</SelectItem>
                          <SelectItem value="Marketing">Marketing</SelectItem>
                          <SelectItem value="Sales">Sales</SelectItem>
                          <SelectItem value="Finance">Finance</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="inviteAs"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Invite as</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger>
                          <span>{field.value || "Select role"}</span>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="member">Member</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="permission"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Permission</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger>
                          <span>{field.value || "Select permission"}</span>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Executer">Executer</SelectItem>
                          <SelectItem value="Editor">Editor</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-4 mt-6">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => console.log("Copy invite link clicked")}
                >
                  Copy invite link
                </Button>

                <PrimaryButton
                  type="submit"
                  className="w-fit px-10"
                  isLoading={isLoading}
                  disabled={isLoading}
                >
                  Send
                </PrimaryButton>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default InviteMemberForm;
