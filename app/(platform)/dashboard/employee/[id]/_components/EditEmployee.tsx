"use client";

import { PencilLineIcon } from "lucide-react";
// import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore"; // Removed

// Renamed component and removed store logic
export const EditEmployeeContent = () => {
  // const { toggleEditMode } = useEmployeeEditStore(); // Removed

  return (
    <>
      {/* Removed button wrapper, now handled by DialogTrigger */}
      <PencilLineIcon className="w-4 h-4" strokeWidth={1.5} />
      <p className="text-sm font-primary">Edit employee</p>
    </>
  );
};
