import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";
import { ChatAvatarProps } from "@/shared/interfaces";

export const ChatAvatar = ({ src, name, className }: ChatAvatarProps) => {
  const fallbackInitial = name ? name.charAt(0).toUpperCase() : "?";

  return (
    <Avatar className={cn(className, "w-8 h-8 flex-shrink-0")}>
      <AvatarImage src={src} />
      <AvatarFallback className="bg-brand-primary text-brand-white-text">
        {fallbackInitial}
      </AvatarFallback>
    </Avatar>
  );
};
