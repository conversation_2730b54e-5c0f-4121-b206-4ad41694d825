"use client";

import Image from "next/image";
import { useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { UserRoundMinusIcon } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { unpublish_employee } from "@/shared/constants";
import { EllipsisVerticalIcon, LoaderIcon } from "lucide-react";
import { agentApi } from "@/app/api/agent";

export const EmployeeAccountDropdown = () => {
  const router = useRouter();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const params = useParams();

  // Get the employee ID from the URL params
  const employeeId = params.id as string;

  const handleUnpublishEmployee = async () => {
    if (employeeId) {
      setLoading(true);
      const response = await agentApi.updateAgentSettings(employeeId, {
        is_bench_employee: true,
      });

      if (response.success) {
        window.location.reload();
      }
    }
  };

  const handleUnpublishClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDropdownOpen(false);
    setIsDialogOpen(true);
  };

  return (
    <>
      <div>
        <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
          <DropdownMenuTrigger className="outline-none">
            <EllipsisVerticalIcon
              className="w-5 h-5 text-brand-primary-font cursor-pointer"
              strokeWidth={1.2}
            />
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="font-primary w-[250px] p-2  "
            align="end"
            alignOffset={5}
            sideOffset={10}
          >
            <DropdownMenuItem
              className="cursor-pointer focus:text-brand-unpublish"
              onClick={handleUnpublishClick}
            >
              <UserRoundMinusIcon className="w-4 h-4 hover:text-brand-unpublish" />
              Unpublish
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="flex flex-col space-y-4 items-center w-full p-6 overflow-hidden bg-background rounded-lg sm:max-w-[425px] md:max-w-[950px] rounded-sm">
            <DialogHeader className="flex flex-row items-center justify-center gap-3 text-center">
              <DialogTitle></DialogTitle>
              <span className="text-card-foreground font-sans text-lg font-semibold leading-7 font-primary">
                Are you sure you want to unpublish this employee?
              </span>
            </DialogHeader>
            <div className="w-2/3 mx-auto flex flex-col items-center justify-center gap-4">
              <Image
                src={unpublish_employee}
                alt="Unpublish Employee UI"
                width={180}
                height={180}
                className="rounded-sm"
              />
              <div className="flex flex-col md:flex-row w-full flex-1 gap-2 mt-4">
                <SecondaryButton
                  className="flex-1"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </SecondaryButton>
                <PrimaryButton
                  className="flex-1"
                  onClick={handleUnpublishEmployee}
                  disabled={loading}
                >
                  {loading ? (
                    <LoaderIcon className="animate-spin" />
                  ) : (
                    "Unpublish"
                  )}
                </PrimaryButton>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};
