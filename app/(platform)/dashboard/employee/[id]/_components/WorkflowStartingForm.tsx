"use client";

import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useWorkflowStore } from "@/hooks/use-workflow";
import { useEmployeeStore } from "@/hooks/use-employee";
import { sanitizeString } from "@/services/helper";

interface WorkflowStartingFormProps {
  onStartWorkflow: (workflowPayload: any, workflowName: string) => void;
}

export const WorkflowStartingForm = ({
  onStartWorkflow,
}: WorkflowStartingFormProps) => {
  const {
    currentWorkflowName,
    workflowValues,
    openWorkflowStartingForm,
    setOpenWorkflowStartingForm,
    updateWorkflowFieldValue,
    getCurrentWorkflow,
  } = useWorkflowStore();

  const { currentEmployeeId } = useEmployeeStore();

  const handleStartWorkflow = () => {
    const selectedWorkflowId = currentEmployeeId
      ? getCurrentWorkflow(currentEmployeeId)
      : null;

    if (!selectedWorkflowId) {
      console.error(
        "WorkflowStartingForm: No current workflow ID found for the employee."
      );
      setOpenWorkflowStartingForm(false);
      return;
    }

    const userDependentFields = workflowValues
      .filter((item) => item.type !== "array")
      .map((item) => item.field);

    const userPayloadTemplate = workflowValues.reduce((acc, item) => {
      acc[item.field] = item.value;
      return acc;
    }, {} as Record<string, any>);

    const workflowPayloadForLiveKit = {
      workflow_id: selectedWorkflowId,
      approval: false,
      payload: {
        user_dependent_fields: userDependentFields,
        user_payload_template: userPayloadTemplate,
      },
    };

    onStartWorkflow(
      workflowPayloadForLiveKit,
      currentWorkflowName || "Unnamed Workflow"
    );
    setOpenWorkflowStartingForm(false);
  };

  // Determine if the form is invalid
  const isFormInvalid = workflowValues.some(
    (field) =>
      field.type !== "array" && // Ignore array types for this validation
      (!field.value ||
        (typeof field.value === "string" && field.value.trim() === ""))
  );

  return (
    <Dialog
      open={openWorkflowStartingForm}
      onOpenChange={setOpenWorkflowStartingForm}
    >
      <DialogContent className="h-[90vh] p-10 min-w-[700px] flex flex-col gap-10 font-primary">
        <DialogHeader>
          <DialogTitle>
            Please fill out the following fields for{" "}
            {sanitizeString(currentWorkflowName ?? "")} workflow
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-8 flex-grow overflow-y-auto">
          {workflowValues
            .filter((value) => value.type !== "array")
            .map((value, index) => (
              <div key={index} className="flex flex-col gap-2">
                <Label>{sanitizeString(value.field)}</Label>
                {value.type === "string" && (
                  <Input
                    type="text"
                    placeholder={sanitizeString(value.field)}
                    onChange={(e) =>
                      updateWorkflowFieldValue(value.field, e.target.value)
                    }
                  />
                )}
                {value.type === "enum" && (
                  <Select
                    onValueChange={(newValue) =>
                      updateWorkflowFieldValue(value.field, newValue)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={sanitizeString(value.field)} />
                    </SelectTrigger>
                    <SelectContent>
                      {value.enum?.map((option) => (
                        <SelectItem key={option} value={option}>
                          {sanitizeString(option)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
            ))}
        </div>
        <DialogFooter>
          <SecondaryButton
            className="w-30"
            onClick={() => setOpenWorkflowStartingForm(false)}
          >
            Cancel
          </SecondaryButton>
          <PrimaryButton
            className="w-30"
            onClick={handleStartWorkflow}
            // disabled={isFormInvalid}
          >
            Start Workflow
          </PrimaryButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
