import {
  useVoiceAssistant,
  BarVisualizer,
  VoiceAssistantControlBar,
  useTranscriptions,
  useRoomContext,
  useLocalParticipant,
} from "@livekit/components-react";
import { useEffect, useRef } from "react";

export const AgentControls = () => {
  const { state, audioTrack } = useVoiceAssistant();
  const room = useRoomContext();
  const { localParticipant } = useLocalParticipant();
  const transcriptions = useTranscriptions();
  const processedTranscriptionsRef = useRef(new Set<string>());

  // Handle user transcriptions and send them to chat
  useEffect(() => {
    if (!room || !localParticipant) return;

    // Filter for user transcriptions that haven't been processed yet
    const userTranscriptions = transcriptions.filter(
      (transcription) =>
        transcription.participantIdentity === localParticipant.identity &&
        transcription.text &&
        transcription.text.trim() &&
        !processedTranscriptionsRef.current.has(transcription.id)
    );

    // Send new user transcriptions to chat
    userTranscriptions.forEach(async (transcription) => {
      try {
        // Mark as processed
        processedTranscriptionsRef.current.add(transcription.id);

        // Send transcription to chat via text stream
        await room.localParticipant.sendText(transcription.text.trim(), {
          topic: "user-transcription",
        });
      } catch (error) {
        console.error("Failed to send user transcription to chat:", error);
      }
    });
  }, [transcriptions, room, localParticipant]);

  return (
    <div className="flex flex-col gap-4 p-2 h-full">
      <div className="flex justify-center items-center h-16">
        <BarVisualizer state={state} barCount={7} trackRef={audioTrack} />
      </div>
      <div className="flex justify-center">
        <VoiceAssistantControlBar />
      </div>
    </div>
  );
};

// Export is now handled via named export
