import {
  useVoiceAssistant,
  BarVisualizer,
  VoiceAssistantControlBar,
} from "@livekit/components-react";
// Transcription functionality temporarily removed

export const AgentControls = () => {
  const { state, audioTrack } = useVoiceAssistant();

  // Transcription handling removed

  return (
    <div className="flex flex-col gap-4 p-2 h-full">
      <div className="flex justify-center items-center h-16">
        <BarVisualizer state={state} barCount={7} trackRef={audioTrack} />
      </div>
      <div className="flex justify-center">
        <VoiceAssistantControlBar />
      </div>
    </div>
  );
};

// Export is now handled via named export
