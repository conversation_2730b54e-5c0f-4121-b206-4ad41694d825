import {
  useVoiceAssistant,
  BarVisualizer,
  VoiceAssistantControlBar,
  useTranscriptions,
  useLocalParticipant,
} from "@livekit/components-react";
import { useEffect, useRef } from "react";

interface AgentControlsProps {
  onUserTranscription?: (text: string) => void;
}

export const AgentControls = ({ onUserTranscription }: AgentControlsProps) => {
  const { state, audioTrack } = useVoiceAssistant();
  const { localParticipant } = useLocalParticipant();
  const transcriptions = useTranscriptions();
  const processedTranscriptionsRef = useRef(new Set<string>());

  // Handle user transcriptions and pass them to parent component
  useEffect(() => {
    if (!localParticipant || !onUserTranscription) return;

    // Filter for user transcriptions that haven't been processed yet
    const userTranscriptions = transcriptions.filter(
      (transcription) =>
        transcription.participantIdentity === localParticipant.identity &&
        transcription.text &&
        transcription.text.trim() &&
        !processedTranscriptionsRef.current.has(transcription.id)
    );

    // Process new user transcriptions
    userTranscriptions.forEach((transcription) => {
      // Mark as processed
      processedTranscriptionsRef.current.add(transcription.id);

      // Pass transcription to parent component
      onUserTranscription(transcription.text.trim());
    });
  }, [transcriptions, localParticipant, onUserTranscription]);

  return (
    <div className="flex flex-col gap-4 p-2 h-full">
      <div className="flex justify-center items-center h-16">
        <BarVisualizer state={state} barCount={7} trackRef={audioTrack} />
      </div>
      <div className="flex justify-center">
        <VoiceAssistantControlBar />
      </div>
    </div>
  );
};

// Export is now handled via named export
