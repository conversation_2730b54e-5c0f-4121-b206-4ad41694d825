"use client";

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { Loader, Trash2 } from "lucide-react";
import { format } from "date-fns";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { communicationApi } from "@/app/api/communication";
import { toast } from "sonner";
import { chatRoute, employeeWindowRoute } from "@/shared/routes";

export const EmployeeConversationHistorySection = () => {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedConversationId, setSelectedConversationId] = useState<
    string | null
  >(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isSummaryDialogOpen, setIsSummaryDialogOpen] = useState(false);
  const [selectedSummary, setSelectedSummary] = useState<string | null>(null);

  const chatIdFromParams = params.chatId as string | undefined;

  const { data: conversations, isLoading } = useQuery({
    queryKey: ["conversations", params.id, page],
    queryFn: () =>
      communicationApi.getConversations({
        agentId: params.id as string,
        page,
        limit: 10,
      }),
    placeholderData: (previousData) => previousData,
  });

  const handleDelete = async () => {
    if (!selectedConversationId) return;

    const isLastConversationOverall =
      conversations?.data?.length === 1 &&
      (conversations?.metadata?.totalPages ?? 1) <= 1 &&
      page === 1;

    try {
      setIsDeleting(true);
      await communicationApi.deleteConversation(selectedConversationId);
      toast.success("Conversation deleted successfully");
      setIsDeleteDialogOpen(false);

      if (isLastConversationOverall) {
        const agentId = params.id as string;
        router.push(`${employeeWindowRoute}/${agentId}`);
      } else {
        await queryClient.invalidateQueries({
          queryKey: ["conversations", params.id],
        });
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to delete conversation");
    } finally {
      setIsDeleting(false);
      setSelectedConversationId(null);
    }
  };

  const openDeleteDialog = (conversationId: string) => {
    setSelectedConversationId(conversationId);
    setIsDeleteDialogOpen(true);
  };

  const navigateToChat = (conversationId: string) => {
    router.push(
      `${employeeWindowRoute}/${params.id}/${chatRoute}/${conversationId}`
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader className="h-8 w-8 animate-spin text-brand-primary" />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 p-8">
      <h2 className="text-xl font-semibold text-brand-primary-font font-primary">
        Past Conversations
      </h2>

      <div className="rounded-lg border border-brand-stroke bg-brand-card">
        <Table className="text-base">
          <TableHeader>
            <TableRow>
              <TableHead className="p-4">Title</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead>Summary</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {conversations?.data.map((conversation) => (
              <TableRow
                key={conversation.id}
                className={`cursor-pointer hover:bg-brand-hover ${
                  chatIdFromParams === conversation.id
                    ? "bg-brand-card-hover"
                    : ""
                }`}
                onClick={() => navigateToChat(conversation.id)}
              >
                <TableCell className="font-medium p-4">
                  {conversation.title.length > 10
                    ? `${conversation.title.substring(0, 10)}...`
                    : conversation.title}
                </TableCell>
                <TableCell className="p-4">
                  {format(new Date(conversation.createdAt), "MMM dd, yyyy")}
                </TableCell>
                <TableCell className="max-w-md truncate">
                  <button
                    className="text-left w-full hover:text-brand-primary transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedSummary(
                        conversation.summary || "No summary available"
                      );
                      setIsSummaryDialogOpen(true);
                    }}
                  >
                    {conversation.summary || "No summary available"}
                  </button>
                </TableCell>
                <TableCell className="text-right">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      openDeleteDialog(conversation.id);
                    }}
                    className="text-brand-unpublish hover:opacity-80"
                  >
                    <Trash2 className="h-5 w-5" />
                  </button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {conversations && conversations.metadata.totalPages > 1 && (
          <div className="flex items-center justify-center gap-2 p-4">
            <SecondaryButton
              onClick={() => setPage((p) => Math.max(1, p - 1))}
              disabled={page === 1}
              className="w-fit px-2"
            >
              Previous
            </SecondaryButton>
            <span className="text-sm text-brand-secondary-font">
              Page {page} of {conversations.metadata.totalPages}
            </span>
            <SecondaryButton
              onClick={() => setPage((p) => p + 1)}
              disabled={page === conversations.metadata.totalPages}
              className="w-fit px-2"
            >
              Next
            </SecondaryButton>
          </div>
        )}
      </div>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Conversation</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this conversation? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <SecondaryButton
              className="w-fit px-2"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </SecondaryButton>
            <PrimaryButton
              onClick={handleDelete}
              isLoading={isDeleting}
              className="w-fit px-2"
            >
              Delete
            </PrimaryButton>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isSummaryDialogOpen} onOpenChange={setIsSummaryDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] flex flex-col bg-brand-background">
          <DialogHeader>
            <DialogTitle className="text-brand-primary text-lg">
              Conversation Summary
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto p-4 flex-grow font-primary">
            <div className="prose prose-base dark:prose-invert max-w-none text-brand-secondary py-1 break-words">
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {selectedSummary || "No summary available"}
              </ReactMarkdown>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
