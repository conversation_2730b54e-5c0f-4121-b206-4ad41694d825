import { render, screen, fireEvent } from "@testing-library/react";
import { EmployeeNavbar } from "../EmployeeNavbar";
import { chatNavbarTabs } from "@/shared/constants";

// Mock the LiveKitButton component
jest.mock("../LiveKitButton", () => ({
  LiveKitButton: () => <div data-testid="livekit-button">LiveKit Button</div>,
}));

// Mock the useParams hook
jest.mock("next/navigation", () => ({
  useParams: () => ({ id: "123" }),
}));

describe("EmployeeNavbar", () => {
  const mockProps = {
    employeeName: "Alice",
    employeeDesignation: "AI Developer",
    employeeAvatar: "/avatars/alice.png",
    onTabChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders employee name and designation correctly", () => {
    render(<EmployeeNavbar {...mockProps} />);
    expect(screen.getByText(/alice.*ai developer/i)).toBeInTheDocument();
  });

  it("renders all tab triggers", () => {
    render(<EmployeeNavbar {...mockProps} />);
    chatNavbarTabs.forEach((tab) => {
      const tabBtn = screen.getByRole("tab", { name: tab.label });
      expect(tabBtn).toBeInTheDocument();
    });
  });

  it("calls onTabChange when a different tab is clicked", () => {
    render(<EmployeeNavbar {...mockProps} />);

    const workflowTab = screen.getByRole("tab", { name: "Workflow" });

    fireEvent.mouseDown(workflowTab); // Radix sometimes needs pointer events
    fireEvent.click(workflowTab);

    // directly asserting inside a small setTimeout to give Radix state time to propagate
    setTimeout(() => {
      expect(mockProps.onTabChange).toHaveBeenCalledWith("Workflow");
    }, 100);
  });

  it("renders the LiveKit button", () => {
    render(<EmployeeNavbar {...mockProps} />);
    expect(screen.getByTestId("livekit-button")).toBeInTheDocument();
  });
});
