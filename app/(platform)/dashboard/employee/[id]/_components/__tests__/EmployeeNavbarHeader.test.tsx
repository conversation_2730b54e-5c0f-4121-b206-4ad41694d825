import { render, screen, fireEvent } from "@testing-library/react";
import { EmployeeNavbarHeader } from "../EmployeeNavbarHeader";
import "@testing-library/jest-dom";

const mockProps = {
  employeeAvatar: "/assets/avatars/alice.png",
  employeeName: "Alice",
  employeeDesignation: "AI Developer",
  onTabChange: jest.fn(),
};

describe("EmployeeNavbarHeader", () => {
  it("renders name, designation, and avatar correctly", () => {
    render(<EmployeeNavbarHeader {...mockProps} />);
    expect(screen.getByText(/Alice the AI Developer/i)).toBeInTheDocument();
    const avatarFallback = screen.getByText("AI");
    expect(avatarFallback).toBeInTheDocument();
  });

  it("opens dialog on click and displays employee info", async () => {
    render(<EmployeeNavbarHeader {...mockProps} />);

    const trigger = screen.getByText(/Alice the AI Developer/i);
    fireEvent.click(trigger);

    // Instead of checking for a name which appears twice, check unique dialog content
    const dialogParagraph = await screen.findByText((content) =>
      content.includes(
        "Hey! I am Alice, an expert in all things related to AI Developer"
      )
    );
    expect(dialogParagraph).toBeInTheDocument();
  });
});
