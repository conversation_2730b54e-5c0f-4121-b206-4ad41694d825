import { render, screen, fireEvent } from "@testing-library/react";
import { ChatToolTab } from "../ChatWorkflowTab";
import { useToolStore } from "@/hooks/use-workflow";
import { useEmployeeStore } from "@/hooks/use-employee";
import { PenToolIcon } from "lucide-react";

// Reset Zustand state before each test
beforeEach(() => {
  // Set a mock employee ID
  const { setCurrentEmployeeId } = useEmployeeStore.getState();
  setCurrentEmployeeId("employee-1");

  // Reset tool selection for this employee
  const { setCurrentToolId } = useToolStore.getState();
  setCurrentToolId("employee-1", null);
});

describe("ChatToolTab", () => {
  const props = {
    id: "tool-1",
    label: "Magic Writer",
    toolIcon: PenToolIcon,
  };

  it("renders the label and icon", () => {
    render(<ChatToolTab {...props} />);
    expect(screen.getByText("Magic Writer")).toBeInTheDocument();
    expect(screen.getByRole("button")).toBeInTheDocument();
  });

  it("updates Zustand state on click", () => {
    render(<ChatToolTab {...props} />);

    // Click the button
    const button = screen.getByRole("button");
    fireEvent.click(button);

    // Zustand should update currentToolId for the current employee
    const { getCurrentToolId } = useToolStore.getState();
    const currentToolId = getCurrentToolId("employee-1");
    expect(currentToolId).toBe("tool-1");
  });

  it("applies active styles if selected", () => {
    // Set current tool before render
    const { setCurrentToolId } = useToolStore.getState();
    setCurrentToolId("employee-1", "tool-1");

    render(<ChatToolTab {...props} />);

    const button = screen.getByRole("button");
    expect(button.className).toContain("border-2");
    expect(button.className).toContain("text-brand-primary-font");
  });
});
