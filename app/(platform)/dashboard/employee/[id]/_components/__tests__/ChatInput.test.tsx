import { render, screen, fireEvent } from "@testing-library/react";
import { ChatInput } from "../ChatInput";
import { useToolStore } from "@/hooks/use-workflow";
import { useEmployeeStore } from "@/hooks/use-employee";

// Set Zustand default state
beforeEach(() => {
  // Set a mock employee ID
  const { setCurrentEmployeeId } = useEmployeeStore.getState();
  setCurrentEmployeeId("employee-1");

  // Set a tool for this employee
  const { setCurrentToolId } = useToolStore.getState();
  setCurrentToolId("employee-1", "tool-1"); // make sure this tool exists in your `tools` array
});

describe("ChatInput", () => {
  const mockOnInputChange = jest.fn();
  const mockOnSendClick = jest.fn();

  it("renders the input with correct placeholder and icon", () => {
    render(
      <ChatInput
        inputValue=""
        onInputChange={mockOnInputChange}
        onSendClick={mockOnSendClick}
      />
    );

    expect(screen.getByPlaceholderText(/send a message/i)).toBeInTheDocument();
  });

  it("allows typing and calls onInputChange", () => {
    render(
      <ChatInput
        inputValue="hello"
        onInputChange={mockOnInputChange}
        onSendClick={mockOnSendClick}
      />
    );

    const input = screen.getByPlaceholderText(/send a message/i);
    fireEvent.change(input, { target: { value: "world" } });
    expect(mockOnInputChange).toHaveBeenCalled();
  });

  it("disables send button when input is empty", () => {
    render(
      <ChatInput
        inputValue="   "
        onInputChange={mockOnInputChange}
        onSendClick={mockOnSendClick}
      />
    );

    const sendButton = screen.getByRole("button");
    expect(sendButton).toBeDisabled();
  });

  it("calls onSendClick when form is submitted", () => {
    render(
      <ChatInput
        inputValue="Let's go"
        onInputChange={mockOnInputChange}
        onSendClick={mockOnSendClick}
      />
    );

    const input = screen.getByPlaceholderText(/send a message/i);
    const form = input.closest("form");
    expect(form).toBeTruthy();

    fireEvent.submit(form!);
    expect(mockOnSendClick).toHaveBeenCalled();
  });
});
