import { render, screen } from "@testing-library/react";
import { ChatBubble } from "../ChatBubble";

describe("ChatBubble", () => {
  const baseProps = {
    message: "Hello! How can I assist you today?",
    senderName: "RUH AI",
    employeeAvatar: "/avatars/bot.png",
  };

  it("renders chat bubble from the assistant correctly", () => {
    render(<ChatBubble {...baseProps} sender="employee" />);

    expect(screen.getByText("RUH AI")).toBeInTheDocument();
    expect(screen.getByText(/how can i assist you today/i)).toBeInTheDocument();
  });

  it("renders chat bubble from the user correctly", () => {
    render(
      <ChatBubble
        {...baseProps}
        sender="user"
        senderName="You"
        employeeAvatar="/avatars/user.png"
      />
    );

    expect(screen.getByText("You")).toBeInTheDocument();
    expect(screen.getByText(/how can i assist you today/i)).toBeInTheDocument();
  });
});
