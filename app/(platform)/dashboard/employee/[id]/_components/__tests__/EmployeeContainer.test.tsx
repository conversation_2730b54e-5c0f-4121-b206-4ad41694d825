import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { useParams } from "next/navigation";
import { useEmployeeStore } from "@/hooks/use-employee";
import { employees, chatNavbarTabs } from "@/shared/constants";
import { EmployeeContainer } from "../EmployeeContainer";

// Mock the next/navigation module
jest.mock("next/navigation", () => ({
  useParams: jest.fn(),
}));

// Mock the child components
jest.mock("../EmployeeNavbar", () => ({
  EmployeeNavbar: ({
    onTabChange,
    employeeName,
    employeeDesignation,
  }: {
    onTabChange: (tab: string) => void;
    employeeName: string;
    employeeDesignation: string;
  }) => (
    <nav data-testid="employee-navbar">
      {chatNavbarTabs.map((tab) => (
        <button
          key={tab.value}
          onClick={() => onTabChange(tab.value)}
          data-testid={`tab-${tab.value.toLowerCase()}`}
        >
          {tab.label}
        </button>
      ))}
      <span>{employeeName}</span>
      <span>{employeeDesignation}</span>
    </nav>
  ),
}));

jest.mock("../EmployeeChatSection", () => ({
  EmployeeChatSection: () => <div data-testid="chat-section">Chat Section</div>,
}));

jest.mock("../EmployeeWorkflowSection", () => ({
  EmployeeWorkflowSection: () => (
    <div data-testid="workflow-section">Workflow Section</div>
  ),
}));

jest.mock("../EmployeeUploadSection", () => ({
  EmployeeFileUploadSection: () => (
    <div data-testid="file-section">File Section</div>
  ),
}));

describe("EmployeeContainer", () => {
  beforeEach(() => {
    // Reset the mock implementation before each test
    (useParams as jest.Mock).mockReturnValue({ id: employees[0].id });
    useEmployeeStore.getState().setCurrentEmployeeId(null);
  });

  it("should set employee ID from URL params and render initial chat section", () => {
    render(<EmployeeContainer />);

    expect(useEmployeeStore.getState().currentEmployeeId).toBe(employees[0].id);
    expect(screen.getByTestId("chat-section")).toBeInTheDocument();
  });

  it("should handle tab changes correctly", () => {
    render(<EmployeeContainer />);

    // Initially chat section should be visible
    expect(screen.getByTestId("chat-section")).toBeInTheDocument();

    // Click workflow tab
    fireEvent.click(screen.getByTestId("tab-workflow"));
    expect(screen.getByTestId("workflow-section")).toBeInTheDocument();
    expect(screen.queryByTestId("chat-section")).not.toBeInTheDocument();

    // Click file tab
    fireEvent.click(screen.getByTestId("tab-file"));
    expect(screen.getByTestId("file-section")).toBeInTheDocument();
    expect(screen.queryByTestId("workflow-section")).not.toBeInTheDocument();

    // Click chat tab
    fireEvent.click(screen.getByTestId("tab-chat"));
    expect(screen.getByTestId("chat-section")).toBeInTheDocument();
    expect(screen.queryByTestId("file-section")).not.toBeInTheDocument();
  });

  it("should display employee information correctly", () => {
    const testEmployee = employees[0];
    (useParams as jest.Mock).mockReturnValue({ id: testEmployee.id });

    render(<EmployeeContainer />);

    expect(screen.getByText(testEmployee.name)).toBeInTheDocument();
    expect(screen.getByText(testEmployee.designation)).toBeInTheDocument();
  });

  it("should handle invalid employee ID", () => {
    const consoleSpy = jest.spyOn(console, "error").mockImplementation();

    (useParams as jest.Mock).mockReturnValue({ id: ["invalid-id"] });

    render(<EmployeeContainer />);

    expect(useEmployeeStore.getState().currentEmployeeId).toBeNull();
    expect(consoleSpy).toHaveBeenCalledWith(
      "Employee ID not found or invalid in URL parameters."
    );

    consoleSpy.mockRestore();
  });
});
