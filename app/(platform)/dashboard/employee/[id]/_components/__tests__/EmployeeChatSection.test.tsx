import { render, screen, fireEvent } from "@testing-library/react";
import { EmployeeChatSection } from "../EmployeeChatSection";

// Mocked props
const mockProps = {
  employeeAvatar: "/avatars/ai-avatar.png",
  employeeDescription: "This is a smart AI assistant.",
  employeeName: "AI Bot",
};

describe("EmployeeChatSection", () => {
  it("renders the starter component when no messages", () => {
    render(<EmployeeChatSection {...mockProps} />);
    expect(
      screen.getByText("This is a smart AI assistant.")
    ).toBeInTheDocument();
  });

  it("allows sending messages and displays them", () => {
    render(<EmployeeChatSection {...mockProps} />);

    const input = screen.getByRole("textbox");
    const sendButton = screen.getByRole("button", { name: "" }); // the icon-only button

    fireEvent.change(input, { target: { value: "Hello AI" } });
    fireEvent.click(sendButton);

    expect(screen.getByText("Hello AI")).toBeInTheDocument();
    expect(
      screen.getByText(
        "Lorem Ipsum is simply dummy text of the printing and typesetting industry."
      )
    ).toBeInTheDocument();
    expect(screen.getByText("You")).toBeInTheDocument();
    expect(screen.getByText("AI Bot")).toBeInTheDocument();
  });

  it("does not send empty messages", () => {
    render(<EmployeeChatSection {...mockProps} />);

    const input = screen.getByRole("textbox");
    const sendButton = screen.getByRole("button", { name: "" });

    fireEvent.change(input, { target: { value: "   " } });
    fireEvent.click(sendButton);

    // It should still show the starter view
    expect(
      screen.getByText("This is a smart AI assistant.")
    ).toBeInTheDocument();
  });
});
