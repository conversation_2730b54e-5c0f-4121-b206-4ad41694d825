import { render, screen, within } from "@testing-library/react";
import { EmployeeChatStarter } from "../EmployeeChatStarter";

// mock tools from constants
jest.mock("@/shared/constants", () => ({
  tools: [
    {
      id: "blog",
      label: "Blog Generation",
      toolIcon: () => <svg data-testid="blog-icon" />,
    },
    {
      id: "video",
      label: "Video Generation",
      toolIcon: () => <svg data-testid="video-icon" />,
    },
  ],
}));

// mock avatar
jest.mock("@/components/shared/EmployeeAvatar", () => ({
  EmployeeAvatar: ({ src }: { src: string }) => (
    <img src={src} alt="Employee Avatar" />
  ),
}));

describe("EmployeeChatStarter", () => {
  const mockProps = {
    employeeAvatar: "/avatar.png",
    employeeDescription: "This is a smart AI assistant.",
  };

  beforeEach(() => {
    render(<EmployeeChatStarter {...mockProps} />);
  });

  it("renders avatar and description", () => {
    expect(screen.getByAltText("Employee Avatar")).toBeInTheDocument();
    expect(screen.getByText(mockProps.employeeDescription)).toBeInTheDocument();
  });

  it("renders instruction and tip", () => {
    expect(
      screen.getByText((content) =>
        content.includes("Click any of these workflows below")
      )
    ).toBeInTheDocument();

    expect(
      screen.getByText(
        (content) => content.includes("Press") && content.includes("/")
      )
    ).toBeInTheDocument();
  });

  it("renders all workflow tool tabs", () => {
    const blogTab = screen.getByText("Blog Generation", { exact: false });
    const videoTab = screen.getByText("Video Generation", { exact: false });

    expect(blogTab).toBeInTheDocument();
    expect(videoTab).toBeInTheDocument();
  });
});
