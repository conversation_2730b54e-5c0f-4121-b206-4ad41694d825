"use client";

import { ApprovalDecision, StepsStatus, WorkflowStatus } from "@/shared/enums";
import {
  ChatMessage,
  LivekitWorkflowUpdateMessageType,
  WorkflowStep,
} from "@/shared/interfaces";
import { sanitizeString } from "@/services/helper";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Check, X } from "lucide-react";
import {
  WorkflowOutputDialog,
  WorkflowOutputDataItem,
} from "./WorkflowOutputDisplay";

interface OutputChatBubbleProps {
  message: ChatMessage;
  workflowStage: WorkflowStatus | null;
  workflowResult: LivekitWorkflowUpdateMessageType | null;
  steps: WorkflowStep[];
  handleApproval: (decision: ApprovalDecision) => void;
}

export const OutputChatBubble = ({
  message,
  workflowStage,
  workflowResult,
  steps,
  handleApproval,
}: OutputChatBubbleProps) => {
  // This is for normal output from the agent
  if (workflowResult === null) {
    return <p>{message?.content as string}</p>;
  }

  // This is for the thinking bubble
  if (workflowStage === WorkflowStatus.RUNNING && steps.length > 0) {
    return <ThinkingBubble steps={steps} workflowStage={workflowStage} />;
  }

  // This is for the workflow result
  if (workflowStage === WorkflowStatus.COMPLETED) {
    return (
      <WorkflowResultBubble
        workflowResult={
          message?.workflowResponse as LivekitWorkflowUpdateMessageType
        }
      />
    );
  }

  // This is for the workflow approval
  if (
    workflowStage === WorkflowStatus.WAITING_FOR_APPROVAL &&
    workflowResult.approval_required
  ) {
    return (
      <WorkflowApprovalBubble
        workflowResult={
          message?.workflowResponse as LivekitWorkflowUpdateMessageType
        }
        handleApproval={handleApproval}
      />
    );
  }

  // This is for the workflow error
  if (
    workflowStage === WorkflowStatus.FAILED ||
    workflowStage === WorkflowStatus.UNKNOWN
  ) {
    return (
      <p className="text-red-500">
        Workflow Execution Failed. Please try again.
      </p>
    );
  }

  // This is for the workflow cancelled
  if (workflowStage === WorkflowStatus.CANCELLED) {
    return <p className="text-red-500">Workflow Execution Cancelled</p>;
  }
};

interface ThinkingBubbleProps {
  steps: WorkflowStep[];
  workflowStage: WorkflowStatus;
}

const ThinkingBubble = ({ steps, workflowStage }: ThinkingBubbleProps) => {
  console.log("steps", steps);
  console.log("workflowStage", workflowStage);
  return (
    <div className="flex flex-col gap-4 p-2">
      {/* Workflow Status Header */}
      <div className="flex items-center gap-2 pb-2 border-b border-brand-border">
        <div
          className={`w-2 h-2 rounded-full ${
            workflowStage === WorkflowStatus.COMPLETED
              ? "bg-green-500"
              : workflowStage === WorkflowStatus.FAILED ||
                workflowStage === WorkflowStatus.UNKNOWN
              ? "bg-red-500"
              : "bg-brand-tertiary animate-pulse"
          }`}
        />
        <p className="text-base font-medium">
          {workflowStage === WorkflowStatus.COMPLETED
            ? "Workflow Execution Completed"
            : workflowStage === WorkflowStatus.FAILED
            ? "Workflow Execution Failed"
            : "Workflow Execution in Progress..."}
        </p>
      </div>

      {/* Sequential Steps */}
      <div className="flex flex-col gap-3">
        {steps.map((step, index) => (
          <div key={step.step} className="flex items-center gap-3 relative">
            {/* Step connector line */}
            {index < steps.length - 1 && (
              <div className="absolute left-[0.9rem] top-6 w-[2px] h-[calc(100%+0.75rem)] bg-brand-border" />
            )}

            {/* Step indicator */}
            <div className="relative z-10 flex items-center justify-center w-7 h-7 rounded-full border-2 border-brand-border bg-background">
              <div
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  step.status === StepsStatus.COMPLETED
                    ? "bg-green-500"
                    : step.status === StepsStatus.FAILED
                    ? "bg-red-500"
                    : "bg-brand-tertiary animate-pulse"
                }`}
              />
            </div>

            {/* Step content */}
            <div className="flex flex-col gap-1">
              <p className="text-sm font-medium">{sanitizeString(step.step)}</p>
              {/* Step status */}
              <p
                className={`text-xs ${
                  step.status === StepsStatus.COMPLETED
                    ? "text-green-500"
                    : step.status === StepsStatus.FAILED
                    ? "text-red-500"
                    : "text-brand-tertiary"
                }`}
              >
                {step.status === StepsStatus.COMPLETED
                  ? "Completed"
                  : step.status === StepsStatus.FAILED
                  ? "Failed"
                  : "Processing..."}
              </p>
              {step.timeLogged && (
                <p className="text-xs text-muted-foreground italic">
                  {step.timeLogged}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

interface WorkflowResultBubbleProps {
  workflowResult: LivekitWorkflowUpdateMessageType;
}

// TODO: Add dialog to see the output
const WorkflowResultBubble = ({
  workflowResult,
}: WorkflowResultBubbleProps) => {
  // workflowResult.result is now correctly typed as WorkflowResultData[]
  // We cast it to WorkflowOutputDataItem[] for the dialog.
  // This assumes WorkflowResultData is structurally compatible with WorkflowOutputDataItem.
  const resultData =
    (workflowResult?.result as unknown as WorkflowOutputDataItem[]) || [];

  return (
    <div className="p-2">
      <p className="mb-2">
        Workflow{" "}
        <span className="font-semibold">
          {sanitizeString(workflowResult.tool_name)}
        </span>{" "}
        completed.
      </p>
      <WorkflowOutputDialog
        resultData={resultData}
        triggerText="View Full Output"
        dialogTitle="Workflow Output"
        toolName={workflowResult.tool_name}
      />
    </div>
  );
};

interface WorkflowApprovalBubbleProps {
  workflowResult: LivekitWorkflowUpdateMessageType;
  handleApproval: (decision: ApprovalDecision) => void;
}

const WorkflowApprovalBubble = ({
  workflowResult,
  handleApproval,
}: WorkflowApprovalBubbleProps) => {
  const [showButtons, setShowButtons] = useState(true);

  const handleApprovalClick = (decision: ApprovalDecision) => {
    handleApproval(decision);
    setShowButtons(false);
  };

  return (
    <div className="flex flex-col gap-4 p-2">
      <p>
        Here is the output for tool:{" "}
        <span className="font-semibold">
          {sanitizeString(workflowResult.tool_name)}
        </span>
      </p>
      <WorkflowOutputDialog
        resultData={
          // workflowResult.result is now correctly typed as WorkflowResultData[]
          (workflowResult?.result as unknown as WorkflowOutputDataItem[]) || []
        }
        triggerText="View Output "
        dialogTitle="Approval Output"
        toolName={workflowResult.tool_name}
      />
      {showButtons && (
        <div className="w-full flex items-center justify-center gap-6">
          <Button
            variant="outline"
            className="bg-green-500 text-white text-xl"
            onClick={() => handleApprovalClick(ApprovalDecision.APPROVE)}
          >
            <Check className="w-4 h-4" />
            Approve
          </Button>
          <Button
            variant="outline"
            className="bg-red-500 text-white text-xl"
            onClick={() => handleApprovalClick(ApprovalDecision.REJECT)}
          >
            <X className="w-4 h-4" />
            Reject
          </Button>
        </div>
      )}
    </div>
  );
};
