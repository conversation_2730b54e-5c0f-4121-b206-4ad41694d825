"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { AgentBase } from "@/shared/interfaces";
import { PencilIcon } from "lucide-react";
import { EditEmployeeProfileForm } from "../../../_components/EditEmployeeProfileForm";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";
import { EditEmployeeGuidelineForm } from "../../../_components/EditEmployeeGuidelineForm";
import { EditEmployeeKnowledgeForm } from "../../../_components/EditEmployeeKnowledgeForm";
import { EditEmployeeToolsForm } from "../../../_components/EditEmployeeToolsForm";
import { EditEmployeeWorkflowForm } from "../../../_components/EditEmployeeWorkflowForm";

const tabs = [
  {
    tab: 1,
    title: "Profile",
  },
  {
    tab: 2,
    title: "Guideline",
  },
  {
    tab: 3,
    title: "Knowledge",
  },
  {
    tab: 4,
    title: "Tools",
  },
  {
    tab: 5,
    title: "Workflow",
  },
];

export const EditEmployeeModal = ({ agent }: { agent: AgentBase }) => {
  const { isModalOpen, closeModal } = useEmployeeEditStore();

  return (
    <Dialog open={isModalOpen} onOpenChange={closeModal}>
      <DialogContent className="min-w-[95vw] h-[95vh]  font-primary overflow-hidden">
        <div className="flex flex-col h-full gap-4">
          <DialogHeader className="px-6 py-4 flex-none">
            <DialogTitle className="text-brand-primary">
              Edit Employee
            </DialogTitle>
          </DialogHeader>

          <Tabs defaultValue="1">
            <TabsList className="w-full justify-evenly bg-transparent border-none ">
              {tabs.map((tab) => (
                <TabsTrigger
                  key={tab.tab}
                  value={tab.tab.toString()}
                  className="data-[state=active]:text-brand-primary data-[state=active]:shadow-none data-[state=active]:bg-transparent text-brand-primary-font border-none hover:bg-transparent"
                >
                  Employee {tab.title}
                </TabsTrigger>
              ))}
            </TabsList>

            <div className="flex-1 overflow-hidden h-full ">
              <TabsContent value="1">
                <EditEmployeeProfileForm agent={agent} />
              </TabsContent>
              <TabsContent value="2">
                <EditEmployeeGuidelineForm agent={agent} />
              </TabsContent>
              <TabsContent value="3">
                <EditEmployeeKnowledgeForm agent={agent} />
              </TabsContent>
              <TabsContent value="4">
                <EditEmployeeToolsForm agent={agent} />
              </TabsContent>
              <TabsContent value="5">
                <EditEmployeeWorkflowForm agent={agent} />
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};
