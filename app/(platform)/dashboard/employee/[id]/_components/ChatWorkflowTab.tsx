"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { ChatWorkflowTabProps } from "@/shared/interfaces";
import { useWorkflowStore } from "@/hooks/use-workflow";
import { useParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { workflowApi } from "@/app/api/workflow";
import { Loader2 } from "lucide-react";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";

export const ChatWorkflowTab: React.FC<ChatWorkflowTabProps> = ({
  label,
  id,
}) => {
  const params = useParams();
  const employeeId = params.id as string;
  const { openModal } = useEmployeeEditStore();

  const { data: workflow, isLoading } = useQuery({
    queryKey: ["workflow", id],
    queryFn: () => workflowApi.getWorkflowsByIds([id]),
  });

  const {
    setOpenWorkflowStartingForm,
    setCurrentWorkflowName,
    setWorkflowValues,
    setCurrentWorkflow,
  } = useWorkflowStore();

  const handleTabClick = (tabId: string) => {
    setCurrentWorkflow(employeeId, tabId);
    setCurrentWorkflowName(workflow?.workflows[0].name || "Workflow");
    setWorkflowValues(workflow?.workflows[0].start_nodes || []);
    setOpenWorkflowStartingForm(true);
  };

  if (isLoading) {
    return (
      <Button
        disabled
        className="flex items-center gap-2 bg-brand-card hover:bg-brand-card/80 text-sm font-medium transition-all h-[36px] text-brand-secondary-font"
      >
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>Loading...</span>
      </Button>
    );
  }

  return (
    <Button
      key={id}
      onClick={() => handleTabClick(id)}
      className="flex items-center gap-2 bg-brand-card hover:bg-brand-card/80 text-sm font-medium transition-all h-[36px] text-brand-secondary-font"
    >
      <span>{label}</span>
    </Button>
  );
};
