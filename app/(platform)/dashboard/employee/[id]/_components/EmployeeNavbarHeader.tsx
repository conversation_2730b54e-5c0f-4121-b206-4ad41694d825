import { Agent } from "@/shared/interfaces";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import Image from "next/image";
import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { ChevronDownIcon } from "lucide-react";

interface EmployeeNavbarHeaderProps {
  agent: Agent;
}

export const EmployeeNavbarHeader = ({ agent }: EmployeeNavbarHeaderProps) => {
  const employeeName = agent.name ?? "Unknown Employee";
  const employeeDesignation = agent.agent_topic_type ?? "AI Agent";

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className="flex flex-row gap-2 items-center cursor-pointer">
          <EmployeeAvatar
            src={agent.avatar}
            name={agent.name}
            className="w-10 h-10"
          />
          <h1 className="text-sm font-semibold text-brand-primary-font">
            {employeeName} the {employeeDesignation}
          </h1>
          <ChevronDownIcon className="w-4 h-4" />
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-[450px]" sideOffset={10} align="start">
        <div className="flex flex-row justify-start items-center gap-4 mb-4">
          <div className="hover:bg-brand-clicked hover:text-brand-primary p-3 rounded-lg flex flex-col items-center justify-start gap-2 bg-brand-clicked text-brand-primary ">
            <EmployeeAvatar
              src={agent.avatar}
              name={agent.name}
              className="w-14 h-14 rounded-sm"
            />
          </div>
          <div className="flex flex-col gap-2">
            <h1 className="text-xl font-semibold text-brand-primary-font">
              {employeeName} the {employeeDesignation}
            </h1>
            <h1 className="text-sm text-brand-secondary-font">
              {agent.description || "AI Assistant"}
            </h1>
          </div>
        </div>
        <div className="text-brand-primary-font text-sm">
          &quot;Hey! I am {employeeName}, an expert in all things related to{" "}
          {employeeDesignation}, and I am excited to help you execute whatever
          you need.&quot;
        </div>
      </PopoverContent>
    </Popover>
  );
};
