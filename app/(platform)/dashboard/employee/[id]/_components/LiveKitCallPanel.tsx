"use client";

import "@livekit/components-styles";
import { RoomAudioRenderer } from "@livekit/components-react";
import { X } from "lucide-react";
import { AgentControls } from "./AgentControls";

interface LiveKitCallPanelProps {
  handleHidePanel: () => void;
}

/**
 * LiveKitCallPanel component
 *
 * Displays the LiveKit call interface (expects to be within a LiveKitRoom context)
 */
export const LiveKitCallPanel = ({
  handleHidePanel,
}: LiveKitCallPanelProps) => {
  return (
    <div className="flex flex-col w-full  h-full border-l border-brand-stroke">
      <div className="flex justify-between items-center p-2 border-b border-brand-stroke">
        <h3 className="text-sm font-medium">Agent Call</h3>
        <button
          onClick={handleHidePanel}
          aria-label="Hide call panel"
          className="p-1 rounded-sm hover:bg-gray-100"
        >
          <X className="h-4 w-4" />
        </button>
      </div>

      <div className="flex-grow overflow-hidden">
        <RoomAudioRenderer />
        <div className="flex-grow overflow-hidden p-2">
          <AgentControls />
        </div>
      </div>
    </div>
  );
};
