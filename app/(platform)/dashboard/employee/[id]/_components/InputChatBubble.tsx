import { LivekitInputMessageType } from "@/shared/interfaces";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Dialog<PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { sanitizeString } from "@/services/helper";

export const InputChatBubble = ({
  message,
}: {
  message: string | LivekitInputMessageType;
}) => {
  // If message is a string, just render it
  if (typeof message === "string") {
    return <p>{message}</p>;
  }

  // Handle LivekitInputMessageType
  if (!message.workflow) {
    return <p>{message.content}</p>;
  }

  const { user_dependent_fields, user_payload_template } =
    message.workflow.payload;

  return (
    <div className="space-y-2">
      <p className="text-brand-primary-font">
        Execute the Workflow with the following data:{" "}
        <Dialog>
          <DialogTrigger asChild>
            <button className="text-brand-primary hover:text-brand-secondary underline transition-colors">
              View Details
            </button>
          </DialogTrigger>
          <DialogContent className="bg-brand-card border-brand-stroke">
            <DialogHeader>
              <DialogTitle className="text-brand-primary font-primary mb-4">
                Workflow Details
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              {user_dependent_fields.map((field) => (
                <div key={field} className="space-y-2">
                  <h3 className="text-brand-secondary font-secondary">
                    {sanitizeString(field)}
                  </h3>
                  <p className="text-brand-primary-font bg-brand-chat-bubble-agent rounded-lg p-3">
                    {typeof user_payload_template[field] === "string"
                      ? sanitizeString(user_payload_template[field])
                      : user_payload_template[field]}
                  </p>
                </div>
              ))}
            </div>
          </DialogContent>
        </Dialog>
      </p>
    </div>
  );
};
