import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { ChatBubbleProps } from "@/shared/interfaces";
import React from "react";
import { ChatAvatar } from "./ChatAvatar";
import { SenderType } from "@/shared/enums";

export const ChatBubble = ({
  sender,
  avatar,
  name,
  children,
}: ChatBubbleProps & { children: React.ReactNode }) => {
  return (
    <div
      className={`flex flex-row gap-2 items-start  ${
        sender === SenderType.USER ? "justify-end" : "justify-start"
      }`}
    >
      {sender === SenderType.USER ? (
        <>
          {/* Message Content */}
          <div
            className={`flex items-start mt-6 p-2  gap-2 min-w-[200px] max-w-[669px] bg-brand-chat-bubble-user rounded-tl-lg rounded-bl-lg rounded-br-lg`}
          >
            <div className="p-3 flex flex-col items-start w-full text-brand-primary-font">
              <div className="text-lg w-full">{children}</div>
            </div>
          </div>
          {/* Avatar Container */}
          <div className={`flex flex-row gap-2 items-center justify-end`}>
            <ChatAvatar src={avatar} name={name} />
          </div>
        </>
      ) : (
        <>
          {/* Avatar Container */}
          <div className={`flex flex-row gap-2 items-center justify-start`}>
            <ChatAvatar src={avatar} name={name} />
          </div>
          {/* Message Content */}
          <div
            className={`flex items-start mt-6 p-2  gap-2 min-w-[200px] max-w-[669px] bg-brand-chat-bubble-agent rounded-tr-lg rounded-br-lg rounded-bl-lg`}
          >
            <div className="p-3 flex flex-col items-start w-full text-brand-primary-font">
              <div className="text-lg w-full">{children}</div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
