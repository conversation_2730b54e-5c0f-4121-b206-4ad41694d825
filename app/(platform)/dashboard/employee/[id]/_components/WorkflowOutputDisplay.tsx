"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { sanitizeString } from "@/services/helper";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

// Define the structure of individual data items from workflow results
export interface WorkflowOutputDataItem {
  property_name: string;
  data_type: "string" | "number" | "object" | "array" | string;
  data: any;
}

interface RenderDataItemProps {
  item: WorkflowOutputDataItem;
}

const RenderDataItem: React.FC<RenderDataItemProps> = ({ item }) => {
  const renderMedia = (url: string, type: "image" | "video" | "audio") => {
    if (type === "image") {
      return (
        <img
          src={url}
          alt={sanitizeString(item.property_name)}
          className="max-w-sm max-h-[500px] h-auto object-contain rounded-md border border-brand-border mx-auto"
        />
      );
    }
    if (type === "video") {
      return (
        <video
          src={url}
          controls
          className="max-w-full max-h-[500px] w-auto h-auto rounded-md border border-brand-border my-1 mx-auto"
        />
      );
    }
    if (type === "audio") {
      return <audio src={url} controls className="w-full my-1" />;
    }
    // Fallback for URL if type detection fails for some reason
    return <p className="text-sm text-brand-secondary break-all py-1">{url}</p>;
  };

  const checkExtension = (data: string): "image" | "video" | "audio" | null => {
    // Ensure data is a string before calling toLowerCase or test
    if (typeof data !== "string") return null;
    const url = data.toLowerCase();
    if (/\.(jpeg|jpg|gif|png|webp|svg)$/.test(url)) return "image";
    if (/\.(mp4|webm|ogg|mov)$/.test(url)) return "video"; // Added mov
    if (/\.(mp3|wav|aac|m4a)$/.test(url)) return "audio"; // Added m4a
    return null;
  };

  if (item.data_type === "string") {
    const mediaType = checkExtension(item.data);
    if (mediaType) {
      return renderMedia(item.data, mediaType);
    }
    // Render string data using ReactMarkdown
    return (
      <div className="prose prose-base dark:prose-invert max-w-none text-brand-secondary py-1 break-words">
        <ReactMarkdown remarkPlugins={[remarkGfm]}>{item.data}</ReactMarkdown>
      </div>
    );
  }

  if (item.data_type === "number") {
    return (
      <p className="text-sm text-brand-secondary py-1">{String(item.data)}</p>
    );
  }

  if (item.data_type === "array" || item.data_type === "object") {
    if (
      Array.isArray(item.data) &&
      item.data.every(
        (subItem): subItem is WorkflowOutputDataItem =>
          typeof subItem === "object" &&
          subItem !== null &&
          "property_name" in subItem &&
          "data_type" in subItem &&
          "data" in subItem
      )
    ) {
      // item.data is an array of WorkflowOutputDataItem
      return (
        <div className="ml-3 pl-3 border-l-2 border-brand-border space-y-2">
          {item.data.map((subItem: WorkflowOutputDataItem, index: number) => (
            <div
              key={index}
              className="p-2 border border-brand-border rounded-md bg-brand-background-muted/50"
            >
              <h5 className="text-sm font-medium text-brand-primary capitalize">
                {sanitizeString(subItem.property_name)}
              </h5>
              <RenderDataItem item={subItem} />
            </div>
          ))}
        </div>
      );
    }
    // Fallback for other objects/arrays - pretty print JSON
    return (
      <pre className="text-sm bg-brand-background-light p-2 rounded-md border border-brand-border whitespace-pre-wrap overflow-x-auto">
        {JSON.stringify(item.data, null, 2)}
      </pre>
    );
  }

  // Fallback for unknown data types or if data is null/undefined
  if (item.data === null || typeof item.data === "undefined") {
    return (
      <p className="text-xs text-muted-foreground italic py-1">
        No data provided
      </p>
    );
  }
  return (
    <p className="text-sm text-red-500 py-1">
      Unsupported data type: {item.data_type} or malformed data.
    </p>
  );
};

interface WorkflowOutputDialogProps {
  // result should be LivekitWorkflowUpdateMessageType['result']
  resultData?: WorkflowOutputDataItem[];
  triggerText: string;
  dialogTitle: string;
  toolName?: string; // Optional tool name for a more specific default title
}

export const WorkflowOutputDialog: React.FC<WorkflowOutputDialogProps> = ({
  resultData,
  triggerText,
  dialogTitle,
  toolName,
}) => {
  console.log("Result Data", resultData);

  if (!Array.isArray(resultData) || resultData.length === 0) {
    return (
      <Button variant="outline" disabled>
        {triggerText} (No data)
      </Button>
    );
  }

  const finalDialogTitle = toolName
    ? `${dialogTitle}: ${sanitizeString(toolName)}`
    : sanitizeString(dialogTitle);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="text-brand-primary border-brand-primary hover:bg-brand-primary/10 hover:text-brand-primary"
        >
          {triggerText}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] md:max-w-[800px] lg:max-w-[1000px] max-h-[90vh] flex flex-col bg-brand-background p-0">
        <DialogHeader className="p-4 border-b border-brand-border">
          <DialogTitle className="text-brand-primary text-lg">
            {finalDialogTitle}
          </DialogTitle>
        </DialogHeader>
        <div className="overflow-y-auto p-4 flex-grow font-primary">
          <div className="space-y-3">
            {resultData.map((item, index) => (
              <div
                key={index}
                className="p-3 border border-brand-border rounded-lg bg-brand-background-muted"
              >
                <h4 className="text-md font-semibold text-brand-primary mb-2 capitalize">
                  {sanitizeString(item.property_name)}
                </h4>
                <RenderDataItem item={item} />
              </div>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
