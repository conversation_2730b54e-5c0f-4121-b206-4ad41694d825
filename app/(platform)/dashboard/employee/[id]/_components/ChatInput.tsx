"use client";

import React, { useEffect, useState, useRef, KeyboardEvent } from "react";
import { Send, Square } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { useEmployeeStore } from "@/hooks/use-employee";
import { useWorkflowStore } from "@/hooks/use-workflow";
import { ChatInputProps, WorkflowInDB } from "@/shared/interfaces";
import { useQuery } from "@tanstack/react-query";
import { workflowApi } from "@/app/api/workflow";
import { sanitizeString } from "@/services/helper";
import { ApprovalDecision } from "@/shared/enums";

export const ChatInput: React.FC<ChatInputProps> = ({
  inputValue,
  onInputChange,
  onSendClick,
  employeeName,
  employeeDesignation,
  employeeWorkflows,
  disabled,
  setIsApprovalRequired,
  handleApproval,
}) => {
  const [isPaletteOpen, setIsPaletteOpen] = useState(false);
  const [filteredWorkflows, setFilteredWorkflows] = useState<WorkflowInDB[]>(
    []
  );
  const [selectedWorkflowIndex, setSelectedWorkflowIndex] = useState(0);
  const [isAutoApprovalEnabled, setIsAutoApprovalEnabled] = useState(true);

  const paletteRef = useRef<HTMLDivElement>(null);
  const selectedItemRef = useRef<HTMLDivElement>(null);

  // Get the current tool ID for this specific agent
  const { currentEmployeeId } = useEmployeeStore();
  const {
    setOpenWorkflowStartingForm,
    setCurrentWorkflowName,
    setWorkflowValues,
    setCurrentWorkflow,
  } = useWorkflowStore();

  // Fetch the current workflow if we have a currentToolId
  const { data: workflowData } = useQuery({
    queryKey: ["workflows", employeeWorkflows],
    queryFn: () => workflowApi.getWorkflowsByIds(employeeWorkflows!),
    enabled: !!employeeWorkflows && employeeWorkflows.length > 0,
  });

  useEffect(() => {
    if (
      inputValue.startsWith("/") &&
      inputValue.length > 0 &&
      (inputValue.length === 1 || inputValue[1] !== " ")
    ) {
      const query = inputValue.substring(1);
      const workflowsToFilter = workflowData?.workflows || [];
      const filtered = workflowsToFilter.filter((wf) =>
        wf.name.toLowerCase().includes(query.toLowerCase())
      );

      if (filtered.length > 0) {
        setFilteredWorkflows(filtered);
        setIsPaletteOpen(true);
        setSelectedWorkflowIndex(0);
      } else {
        setIsPaletteOpen(false);
        setFilteredWorkflows([]);
      }
    } else {
      setIsPaletteOpen(false);
      setFilteredWorkflows([]);
    }
  }, [inputValue, workflowData?.workflows]);

  useEffect(() => {
    if (selectedItemRef.current) {
      selectedItemRef.current.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
      });
    }
  }, [selectedWorkflowIndex]);

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    if (isPaletteOpen && filteredWorkflows.length > 0) {
      if (event.key === "ArrowDown") {
        event.preventDefault();
        setSelectedWorkflowIndex(
          (prev) => (prev + 1) % filteredWorkflows.length
        );
      } else if (event.key === "ArrowUp") {
        event.preventDefault();
        setSelectedWorkflowIndex(
          (prev) =>
            (prev - 1 + filteredWorkflows.length) % filteredWorkflows.length
        );
      } else if (event.key === "Enter") {
        event.preventDefault();
        const selectedWorkflow = filteredWorkflows[selectedWorkflowIndex];
        if (selectedWorkflow && currentEmployeeId) {
          console.log("Selected Workflow ID:", selectedWorkflow.id);

          setCurrentWorkflow(currentEmployeeId, selectedWorkflow.id);
          setCurrentWorkflowName(selectedWorkflow.name);
          setWorkflowValues(selectedWorkflow.start_nodes || []);
          setOpenWorkflowStartingForm(true);

          const syntheticEvent = {
            target: { value: "" },
          } as React.ChangeEvent<HTMLInputElement>;
          onInputChange(syntheticEvent);
          setIsPaletteOpen(false);
        }
      } else if (event.key === "Escape") {
        event.preventDefault();
        setIsPaletteOpen(false);
      }
    }
  };

  const handlePaletteItemClick = (workflow: WorkflowInDB) => {
    if (currentEmployeeId) {
      console.log("Selected Workflow ID:", workflow.id);

      setCurrentWorkflow(currentEmployeeId, workflow.id);
      setCurrentWorkflowName(workflow.name);
      setWorkflowValues(workflow.start_nodes || []);
      setOpenWorkflowStartingForm(true);

      const syntheticEvent = {
        target: { value: "" },
      } as React.ChangeEvent<HTMLInputElement>;
      onInputChange(syntheticEvent);
      setIsPaletteOpen(false);
    }
  };

  const handleFormSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    onSendClick();
  };

  return (
    <div className="w-full px-4 pt-4 h-60 flex items-start justify-center ">
      <div className="relative flex flex-col w-full bg-brand-card border border-brand-stroke rounded-lg p-2 gap-2">
        <div className="flex items-center px-2 mb-1">
          <div className="flex items-center space-x-2">
            <Switch
              id="auto-approval"
              checked={isAutoApprovalEnabled}
              onCheckedChange={(checked) => {
                setIsAutoApprovalEnabled(checked);
                setIsApprovalRequired(!checked);
              }}
            />
            <label
              htmlFor="auto-approval"
              className="text-sm text-brand-primary-font"
            >
              Auto approval
            </label>
          </div>
        </div>
        {isPaletteOpen && filteredWorkflows.length > 0 && (
          <div
            ref={paletteRef}
            className="absolute font-primary p-2 bottom-full left-0 right-0 mb-2 w-full max-h-48 overflow-y-auto bg-brand-card border border-brand-stroke rounded-lg shadow-xl p-1 z-10"
          >
            {filteredWorkflows.map((wf, index) => (
              <div
                key={wf.id}
                ref={index === selectedWorkflowIndex ? selectedItemRef : null}
                onClick={() => handlePaletteItemClick(wf)}
                onMouseEnter={() => setSelectedWorkflowIndex(index)}
                className={`p-2 cursor-pointer rounded-md text-brand-primary-font hover:bg-brand-card-hover ${
                  index === selectedWorkflowIndex ? "bg-brand-clicked" : ""
                }`}
              >
                {sanitizeString(wf.name)}
              </div>
            ))}
          </div>
        )}
        <form
          onSubmit={handleFormSubmit}
          className="flex flex-1 items-center gap-2"
        >
          <Input
            type="text"
            value={inputValue}
            onChange={onInputChange}
            onKeyDown={handleKeyDown}
            placeholder={
              disabled
                ? "Please wait while response is being generated..."
                : `Start chatting with ${employeeName} the ${employeeDesignation} here`
            }
            className="flex-1 min-w-0 border-none rounded-lg focus:outline-none focus:ring-0 text-base bg-transparent"
            style={{ boxShadow: "none" }}
            disabled={disabled}
          />
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={() => {
              if (disabled) {
                handleApproval(ApprovalDecision.REJECT);
              } else {
                onSendClick();
              }
            }}
            className="shrink-0  text-brand-primary-font disabled:opacity-50"
            disabled={!disabled && inputValue.trim() === ""}
          >
            {disabled ? (
              <Square className="h-5 w-5 text-red-500" fill="red" />
            ) : (
              <Send className="h-5 w-5" />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
};
