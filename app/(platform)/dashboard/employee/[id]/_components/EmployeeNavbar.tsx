import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { chatNavbarTabs } from "@/shared/constants";
import { Agent } from "@/shared/interfaces";
import { HelpCircleIcon, PencilIcon, PlusIcon } from "lucide-react";
import { EmployeeNavbarHeader } from "./EmployeeNavbarHeader";
import { activeTabTriggerClasses } from "./style";
import { AgentCallButton } from "./AgentCallButton";
import { EditEmployeeModal } from "./EditEmployeeModal";
import { EmployeeAccountDropdown } from "./EmployeeAccountDropdown";
import { HelpModal } from "@/components/shared/HelpModal";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";
import { useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { communicationApi } from "@/app/api/communication";
import { toast } from "sonner";
import { employeeWindowRout<PERSON>, chat<PERSON>oute } from "@/shared/routes";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { agentApi } from "@/app/api/agent";

interface EmployeeNavbarProps {
  agent: Agent;
  onTabChange?: (value: string) => void;
}

export const EmployeeNavbar = ({ agent, onTabChange }: EmployeeNavbarProps) => {
  const { openModal } = useEmployeeEditStore();
  const router = useRouter();
  const params = useParams();
  const [isCreatingChat, setIsCreatingChat] = useState(false);

  const agentId = Array.isArray(params.id) ? params.id[0] : params.id;

  const handleNewChat = async () => {
    if (!agentId) {
      toast.error("Agent ID not found. Cannot create new chat.");
      return;
    }
    setIsCreatingChat(true);
    try {
      const newConversationId = await communicationApi.createConversation(
        agentId
      );
      toast.success("New chat created successfully!");
      router.push(
        `${employeeWindowRoute}/${agentId}${chatRoute}/${newConversationId}`
      );
    } catch (error: any) {
      toast.error(
        error.message || "Failed to create new chat. Please try again."
      );
    } finally {
      setIsCreatingChat(false);
    }
  };

  const handleDeleteAgent = async () => {
    if (!agentId) {
      toast.error("Agent ID not found. Cannot delete agent.");
      return;
    }
    await agentApi.deleteAgent(agentId);
  };

  return (
    <div className="bg-brand-card border-b border-brand-stroke px-6 pt-4 flex flex-col gap-4">
      <div className="flex flex-row justify-between">
        <EmployeeNavbarHeader agent={agent} />
        <div className="flex items-center justify-center gap-8 pr-6">
          {/* <Button
            variant="destructive"
            className="font-primary text-center"
            onClick={handleDeleteAgent}
          >
            Delete Agent
          </Button> */}
          <HelpModal>
            <button className="flex flex-row items-center gap-2">
              <HelpCircleIcon className="w-4 h-4" />
              <h1 className="text-sm text-brand-primary-font font-primary">
                Help
              </h1>
            </button>
          </HelpModal>

          <button
            onClick={openModal}
            className="flex items-center justify-center gap-2 p-1 rounded-xs p-1 font-primary text-sm min-w-[150px]"
          >
            <PencilIcon className="w-4 h-4" />
            Edit Employee
          </button>
          <EditEmployeeModal agent={agent} />
          <EmployeeAccountDropdown />
        </div>
      </div>

      <div className="flex flex-row justify-between w-full pr-6 ">
        <Tabs defaultValue="Chat" onValueChange={onTabChange} className="w-fit">
          <TabsList className="w-full justify-start bg-transparent">
            {chatNavbarTabs.map((tab) => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className={activeTabTriggerClasses}
              >
                <tab.icon className="w-4 h-4" />
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
        <div className="flex gap-4 pb-2">
          <button
            onClick={handleNewChat}
            disabled={isCreatingChat}
            className="flex items-center justify-center gap-2 p-1 border-brand-stroke border rounded-xs p-1 font-primary text-sm min-w-[150px]"
          >
            {isCreatingChat ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <PlusIcon className="w-4 h-4 text-brand-secondary-font" />
            )}
            {isCreatingChat ? "Creating..." : "New Chat"}
          </button>
          <AgentCallButton />
        </div>
      </div>
    </div>
  );
};
