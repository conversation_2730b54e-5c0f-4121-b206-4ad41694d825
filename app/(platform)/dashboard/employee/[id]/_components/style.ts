export const activeTabTriggerClasses =
  "relative flex items-center py-4 px-6 gap-2 text-brand-primary-font font-medium data-[state=active]:text-brand-primary dark:data-[state=active]:text-brand-primary " +
  "data-[state=active]:after:content-[''] data-[state=active]:after:absolute data-[state=active]:after:-bottom-px data-[state=active]:after:left-1/2 data-[state=active]:after:-translate-x-1/2 " +
  "data-[state=active]:after:w-4/5 data-[state=active]:after:h-[1.5px] data-[state=active]:after:bg-brand-primary " +
  "dark:data-[state=active]:after:h-[2px]";
