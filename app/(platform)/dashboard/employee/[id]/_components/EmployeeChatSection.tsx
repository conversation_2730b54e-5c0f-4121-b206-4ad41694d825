"use client";

import React, { useEffect, useState, useRef } from "react";
import { ChatMessage, Agent } from "@/shared/interfaces";
import { useCallStore } from "@/hooks/useCallStore";
import { LiveKitCallPanel } from "./LiveKitCallPanel";
import { livekitApi } from "@/app/api/livekit";
import { toast } from "sonner";
import { LiveKitRoom } from "@livekit/components-react";
import { ChatInterface, ChatInterfaceRef } from "./ChatInterface";
import { Skeleton } from "@/components/ui/skeleton";
import { useParams } from "next/navigation";

interface EmployeeChatSectionProps {
  agent: Agent;
}

export const EmployeeChatSection = ({ agent }: EmployeeChatSectionProps) => {
  const { isCallPanelVisible, setIsCallPanelVisible } = useCallStore();
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const chatInterfaceRef = useRef<ChatInterfaceRef>(null);

  const params = useParams();

  const conversationId = params.chatId as string;

  const employeeName = agent.name ?? "Unknown Employee";
  const employeeDesignation = agent.agent_topic_type ?? "AI Agent";
  const employeeDescription = agent.description ?? "AI Assistant";
  const userAvatar = "/assets/dashboard/pfp.svg";
  const userName = "You";

  useEffect(() => {
    setIsLoading(true);
    let isMounted = true; // Flag to prevent state updates on unmounted component
    livekitApi
      .getToken(agent.id, "single", conversationId)
      .then((newToken) => {
        if (isMounted) {
          setToken(newToken);
        }
      })
      .catch(() => {
        if (isMounted) {
          toast.error("Could not initialize connection to AI agent");
        }
      })
      .finally(() => {
        if (isMounted) {
          setIsLoading(false);
        }
      });

    // Cleanup function
    return () => {
      isMounted = false; // Set flag to false when unmounting
      setIsCallPanelVisible(false); // Reset panel visibility on unmount
    };
  }, [setIsCallPanelVisible]);

  const handleDisconnect = () => {
    console.warn("LiveKit connection disconnected unexpectedly");
    setToken(null);
    setIsCallPanelVisible(false);
    setIsLoading(true); // Set loading to true to show loading/error state
    toast.error("Connection to agent lost. Please refresh.");
  };

  const handleHidePanel = () => {
    setIsCallPanelVisible(false);
  };

  const handleUserTranscription = (text: string) => {
    if (chatInterfaceRef.current) {
      chatInterfaceRef.current.addUserTranscription(text);
    }
  };

  // --- Loading State for Token ---
  if (isLoading) {
    // Show loading only if token isn't available yet
    return (
      <div className="flex items-center justify-center h-full w-full">
        <div className="flex flex-col items-center space-y-2">
          <Skeleton className="h-12 w-12 rounded-full" />
          <Skeleton className="h-4 w-[250px]" />
          <Skeleton className="h-4 w-[200px]" />
          <p className="text-sm text-gray-500">Initializing connection...</p>
        </div>
      </div>
    );
  }

  // --- Error State (No Token after loading) ---
  if (!token) {
    return (
      <div className="flex items-center justify-center h-full w-full overflow-y-auto">
        <p className="text-red-500">
          Failed to connect. Please refresh the page.
        </p>
      </div>
    );
  }

  // --- Main Connected View ---
  return (
    <LiveKitRoom
      serverUrl={process.env.NEXT_PUBLIC_LIVEKIT_URL}
      token={token}
      connect={true}
      video={false}
      audio={false}
      onDisconnected={handleDisconnect}
      className="flex mx-auto h-full w-full relative"
    >
      <div
        className={`flex flex-col h-full ${
          isCallPanelVisible ? "flex-1" : "w-full max-w-6xl mx-auto"
        } `}
      >
        <ChatInterface ref={chatInterfaceRef} agent={agent} />
      </div>

      {isCallPanelVisible && (
        <div className="w-[400px] flex-shrink-0 h-full border-l border-gray-200 ">
          <LiveKitCallPanel
            handleHidePanel={handleHidePanel}
            onUserTranscription={handleUserTranscription}
          />
        </div>
      )}
    </LiveKitRoom>
  );
};
