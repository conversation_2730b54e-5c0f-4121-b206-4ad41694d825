"use client";

import { ChatWorkflowTab } from "./ChatWorkflowTab";
import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { EmployeeChatStarterProps, WorkflowInDB } from "@/shared/interfaces";
import { ChatBubble } from "./ChatBubble";
import { useUserStore } from "@/hooks/use-user";
import { useEffect, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { workflowApi } from "@/app/api/workflow";
import { sanitizeString } from "@/services/helper";

export const EmployeeChatStarter = ({
  employeeName,
  employeeAvatar,
  employeeDescription,
  employeeWorkflows,
}: EmployeeChatStarterProps) => {
  const [workflows, setWorkflows] = useState<WorkflowInDB[]>([]);
  const { user } = useUserStore();

  const queryClient = useQueryClient();

  // Fetch workflows by ids
  useEffect(() => {
    const fetchWorkflows = async () => {
      if (employeeWorkflows) {
        const workflows = await queryClient.fetchQuery({
          queryKey: ["workflows", employeeWorkflows],
          queryFn: () => workflowApi.getWorkflowsByIds(employeeWorkflows),
        });
        setWorkflows(workflows.workflows);
      }
    };
    fetchWorkflows();
  }, [employeeWorkflows]);

  return (
    <div className="flex flex-col items-center text-center gap-6 h-full justify-center">
      {user?.isFirstLogin && (
        <h1 className="font-primary text-gradient-brand text-2xl font-bold">
          Welcome to Ruh.AI!
        </h1>
      )}
      <EmployeeAvatar src={employeeAvatar} className="w-24 h-24 " />
      <h1 className="text-lg font-semibold text-brand-primary-font max-w-[800px] ">
        This is {employeeName} , your new {employeeDescription}
      </h1>
      <div className="w-full">
        <ChatBubble
          sender="employee"
          avatar={employeeAvatar}
          name={`${employeeName}`}
        >
          <div className="flex flex-col items-start gap-8 w-full p-2 ">
            <h1 className="text-lg text-brand-primary-font text-left">
              I can create masterful blogs, high quality videos, and a unique
              social media strategy for your brand, along with content (and
              that's just the tip of the iceberg!)
            </h1>

            <div className="flex flex-col gap-6 pl-4 items-start">
              <h1 className="text-lg text-brand-primary-font text-left">
                To fully explore what i can do, click any of this workflows
                below to get started
              </h1>

              <div className="flex flex-row gap-2 pt-1 pb-2 flex-wrap justify-start">
                {workflows.length === 0 && <p>No workflows found</p>}
                {workflows.slice(0, 3).map((workflow) => (
                  <ChatWorkflowTab
                    key={workflow.id}
                    label={sanitizeString(workflow.name)}
                    id={workflow.id}
                  />
                ))}
              </div>

              <p>
                (and a future mid-convo tip: Press &quot;/&quot; to view all
                workflows I can execute)
              </p>
            </div>
          </div>
        </ChatBubble>
      </div>
    </div>
  );
};
