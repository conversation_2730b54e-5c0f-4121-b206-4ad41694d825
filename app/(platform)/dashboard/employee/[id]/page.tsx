"use client";

import { usePara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { communicationApi } from "@/app/api/communication";
import { chatRoute } from "@/shared/routes";
import { employeeWindowRoute } from "@/shared/routes";
import { LoaderIcon } from "lucide-react";

export default function EmployeeWindow() {
  const params = useParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  const agentIdParam = params.id;
  const agentId = Array.isArray(agentIdParam) ? agentIdParam[0] : agentIdParam;

  useEffect(() => {
    if (!agentId) {
      setIsLoading(false);
      return;
    }

    const fetchAndNavigate = async () => {
      setIsLoading(true);
      try {
        const conversationsData = await communicationApi.getConversations({
          agentId,
        });

        if (
          conversationsData &&
          conversationsData.data &&
          conversationsData.data.length > 0
        ) {
          const latestConversationId = conversationsData.data[0].id;
          router.push(
            `${employeeWindowRoute}/${params.id}/${chatRoute}/${latestConversationId}`
          );
        } else {
          const newConversationId = await communicationApi.createConversation(
            agentId
          );
          router.push(
            `${employeeWindowRoute}/${params.id}/${chatRoute}/${newConversationId}`
          );
        }
        // Component will unmount on successful navigation, so setIsLoading(false) may not be strictly needed here.
      } catch (error) {
        console.error("Failed to fetch or create conversation:", error);
        setIsLoading(false); // Stop loading and show error message below
      }
    };

    fetchAndNavigate();
  }, [agentId, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen h-screen flex flex-col gap-4 items-center justify-center">
        <LoaderIcon className="w-10 h-10 animate-spin text-brand-primary" />
        <p className="text-base font-medium text-brand-primary-font">
          Please wait while we load the employee chat...
        </p>
      </div>
    );
  }

  // This content is shown if agentId is initially missing, or if an error occurs during API calls.
  return (
    <div className="min-h-screen h-screen flex items-center justify-center">
      <p>
        Unable to load employee chat. Please check the URL or try again later.
      </p>
    </div>
  );
}
