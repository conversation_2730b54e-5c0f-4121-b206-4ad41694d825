"use client";

import React, { useState, useEffect } from "react";
import { ChatBubble } from "../_components/ChatBubble"; // Assuming ChatBubble.tsx is in _components
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog"; // For OutputChatBubble

// --- Mocked Enums & Types (Ideally import from shared locations) ---
const stepsStatus = {
  CONNECTING: "CONNECTING",
  COMPLETED: "COMPLETED",
  FAILED: "FAILED",
  ERROR: "ERROR",
  PROCESSING: "PROCESSING", // For steps that are neither completed nor failed
  TIME_LOGGED: "TIME_LOGGED", // This is actually an event type in original code
};
const workflowStatus = {
  STARTED: "STARTED",
  COMPLETED: "COMPLETED",
  FAILED: "FAILED",
  ERROR: "ERROR",
  IN_PROGRESS: "IN_PROGRESS", // Custom for clarity
};

type StepsStatus = (typeof stepsStatus)[keyof typeof stepsStatus];
type WorkflowStatus = (typeof workflowStatus)[keyof typeof workflowStatus];

interface ChatMessage {
  id: number;
  text: string;
  sender: "user" | "employee";
  avatar?: string;
  name?: string;
}

interface WorkflowStep {
  step: string;
  status: StepsStatus;
  timeLogged?: string;
}

interface ScriptGenerationResultItem {
  data: string;
  data_type: string;
  property_name: string;
}

interface ScriptGenerationResult {
  data: string | number | ScriptGenerationResultItem[];
  data_type: string;
  property_name: string;
}

interface WorkflowResult {
  transition_id: string;
  node_id: string;
  tool_name: string;
  result: ScriptGenerationResult[];
  approval_required: boolean;
  status: string; // Status of the step producing the result
  sequence: number;
  workflow_status: string; // Overall workflow status
}

// --- Copied Component Definitions (from ChatInterface.tsx) ---

// LoadingBubble Component
const LoadingBubble = () => {
  return (
    <div className="flex items-center justify-center py-2">
      <div className="flex gap-2">
        <div
          className="w-2 h-2 rounded-full bg-brand-primary animate-bounce"
          style={{ animationDelay: "0ms" }}
        />
        <div
          className="w-2 h-2 rounded-full bg-brand-primary animate-bounce"
          style={{ animationDelay: "150ms" }}
        />
        <div
          className="w-2 h-2 rounded-full bg-brand-primary animate-bounce"
          style={{ animationDelay: "300ms" }}
        />
      </div>
    </div>
  );
};

// ThinkingBubble Component
interface ThinkingBubbleProps {
  steps: WorkflowStep[];
  workflowStage: WorkflowStatus;
}
const ThinkingBubble = ({ steps, workflowStage }: ThinkingBubbleProps) => {
  return (
    <div className="flex flex-col gap-4 p-2 font-primary">
      <div className="flex items-center gap-2 pb-2 border-b border-brand-border">
        <div
          className={`w-2 h-2 rounded-full ${
            workflowStage === workflowStatus.COMPLETED
              ? "bg-green-500"
              : workflowStage === workflowStatus.FAILED
              ? "bg-red-500"
              : "bg-brand-tertiary animate-pulse"
          }`}
        />
        <p className="text-sm font-medium">
          {workflowStage === workflowStatus.COMPLETED
            ? "Workflow Completed"
            : workflowStage === workflowStatus.FAILED
            ? "Workflow Failed"
            : "Workflow in Progress..."}
        </p>
      </div>
      <div className="flex flex-col gap-3">
        {steps.map((step, index) => (
          <div
            key={step.step + index}
            className="flex items-center gap-3 relative"
          >
            {index < steps.length - 1 && (
              <div className="absolute left-[0.9rem] top-6 w-[2px] h-[calc(100%+0.75rem)] bg-brand-border" />
            )}
            <div className="relative z-10 flex items-center justify-center w-7 h-7 rounded-full border-2 border-brand-border bg-background">
              <div
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  step.status === stepsStatus.COMPLETED
                    ? "bg-green-500"
                    : step.status === stepsStatus.FAILED ||
                      step.status === stepsStatus.ERROR
                    ? "bg-red-500"
                    : "bg-brand-tertiary animate-pulse" // Default for CONNECTING, PROCESSING etc.
                }`}
              />
            </div>
            <div className="flex flex-col gap-1">
              <p className="text-sm font-medium">{step.step}</p>
              <p
                className={`text-xs ${
                  step.status === stepsStatus.COMPLETED
                    ? "text-green-500"
                    : step.status === stepsStatus.FAILED ||
                      step.status === stepsStatus.ERROR
                    ? "text-red-500"
                    : "text-brand-tertiary"
                }`}
              >
                {step.status === stepsStatus.COMPLETED
                  ? "Completed"
                  : step.status === stepsStatus.FAILED ||
                    step.status === stepsStatus.ERROR
                  ? "Failed"
                  : step.status === stepsStatus.CONNECTING
                  ? "Connecting..."
                  : "Processing..."}
              </p>
              {step.timeLogged && (
                <p className="text-xs text-muted-foreground italic">
                  {step.timeLogged}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// OutputChatBubble Component
interface OutputChatBubbleProps {
  transition_id: string;
  node_id: string;
  tool_name: string;
  result: ScriptGenerationResult[];
  approval_required: boolean;
  status: string;
  sequence: number;
  workflow_status: string;
}

const OutputChatBubble = (props: OutputChatBubbleProps) => {
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);

  const renderContent = (item: ScriptGenerationResult) => {
    // Handle nested object data type
    if (item.data_type === "object" && Array.isArray(item.data)) {
      const urlItem = item.data.find((d) => d.property_name === "url");
      const mimetypeItem = item.data.find(
        (d) => d.property_name === "mimetype"
      );

      if (
        urlItem &&
        mimetypeItem &&
        typeof urlItem.data === "string" &&
        typeof mimetypeItem.data === "string"
      ) {
        const url = urlItem.data;
        const mimetype = mimetypeItem.data;

        if (mimetype.startsWith("image/")) {
          return (
            <img
              src={url}
              alt={item.property_name}
              className="rounded-lg max-h-[400px] object-contain bg-brand-chat-bubble-agent"
            />
          );
        }

        if (mimetype.startsWith("video/")) {
          return (
            <video
              controls
              src={url}
              className="rounded-lg w-full bg-brand-chat-bubble-agent"
            >
              Your browser does not support the video tag.
            </video>
          );
        }

        if (mimetype.startsWith("audio/")) {
          return (
            <div className="p-4 rounded-lg bg-brand-chat-bubble-agent">
              <audio controls src={url} className="w-full">
                Your browser does not support the audio element.
                <a href={url} className="text-brand-primary hover:underline">
                  Download audio
                </a>
              </audio>
            </div>
          );
        }
      }

      // Fallback for object type without recognized mimetype
      return (
        <div className="p-4 rounded-lg bg-brand-chat-bubble-agent whitespace-pre-wrap text-brand-primary-font">
          {JSON.stringify(item.data, null, 2)}
        </div>
      );
    }

    // Handle number type
    if (item.data_type === "number" && typeof item.data === "number") {
      return (
        <div className="p-4 rounded-lg bg-brand-chat-bubble-agent text-brand-primary-font">
          {item.data.toFixed(2)}
        </div>
      );
    }

    // Handle legacy video type
    if (typeof item.data === "string" && item.data_type.startsWith("video/")) {
      return (
        <video
          controls
          width="100%"
          src={item.data}
          className="rounded-lg bg-brand-chat-bubble-agent"
        >
          Your browser does not support the video tag.
        </video>
      );
    }

    // Handle legacy audio type
    if (typeof item.data === "string" && item.data_type.startsWith("audio/")) {
      return (
        <div className="p-4 rounded-lg bg-brand-chat-bubble-agent">
          <audio controls src={item.data} className="w-full">
            Your browser does not support the audio element.
            <a href={item.data} className="text-brand-primary hover:underline">
              Download audio
            </a>
          </audio>
        </div>
      );
    }

    // Default text display for other types
    return (
      <div className="p-4 rounded-lg bg-brand-chat-bubble-agent whitespace-pre-wrap text-brand-primary-font">
        {typeof item.data === "string"
          ? item.data
          : JSON.stringify(item.data, null, 2)}
      </div>
    );
  };

  return (
    <>
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-2">
          <button
            onClick={() => setIsDialogOpen(true)}
            className="px-4 py-2 text-sm font-medium text-brand-white-text bg-brand-primary rounded-md hover:bg-brand-primary/90 transition-colors"
          >
            View Generated Output
          </button>
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto bg-brand-card border-brand-border">
          <DialogHeader>
            <DialogTitle className="text-brand-primary-font">
              Generated Output Details
            </DialogTitle>
            <DialogDescription className="text-brand-secondary-font">
              Review the generated output and related information. Transition
              ID: {props.transition_id}
            </DialogDescription>
          </DialogHeader>

          <div className="flex flex-col gap-6 mt-4">
            {props.result.map((item, index) => (
              <div key={index} className="flex flex-col gap-2">
                <h3 className="text-lg font-semibold capitalize text-brand-primary">
                  {item.property_name.replace(/_/g, " ")}
                </h3>
                {renderContent(item)}
              </div>
            ))}
          </div>

          <DialogFooter className="mt-6">
            <button
              onClick={() => setIsDialogOpen(false)}
              className="px-4 py-2 text-sm font-medium text-brand-primary-font bg-brand-chat-bubble-agent rounded-md hover:bg-brand-chat-bubble-agent/90 transition-colors"
            >
              Close
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

// --- Helper for JSON Display ---
const JsonDisplay = ({ data, title }: { data: any; title: string }) => (
  <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow">
    <h4 className="text-md font-semibold text-gray-700 dark:text-gray-300 mb-2 border-b pb-1 border-gray-300 dark:border-gray-600">
      {title}
    </h4>
    <pre className="text-xs overflow-x-auto text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-900 p-2 rounded">
      {JSON.stringify(data, null, 2)}
    </pre>
  </div>
);

// --- Static Data Definitions ---
const DUMMY_AGENT_AVATAR =
  "https://via.placeholder.com/40/007bff/ffffff?text=AI";
const DUMMY_USER_AVATAR = "https://via.placeholder.com/40/28a745/ffffff?text=U";
const DUMMY_AGENT_NAME = "Test Agent";
const DUMMY_USER_NAME = "Test User";

const normalUserMessage: ChatMessage = {
  id: 1,
  text: "Hello, can you help me with testing?",
  sender: "user",
  avatar: DUMMY_USER_AVATAR,
  name: DUMMY_USER_NAME,
};
const normalEmployeeMessage: ChatMessage = {
  id: 2,
  text: "Yes, I'm ready to display various chat components!",
  sender: "employee",
  avatar: DUMMY_AGENT_AVATAR,
  name: DUMMY_AGENT_NAME,
};
const longEmployeeMessage: ChatMessage = {
  id: 3,
  text: "This is a much longer message from the employee to demonstrate how text wrapping works within the ChatBubble component. It should handle multiple lines gracefully and maintain readability. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  sender: "employee",
  avatar: DUMMY_AGENT_AVATAR,
  name: DUMMY_AGENT_NAME,
};

const thinkingBubbleData = {
  initial: {
    steps: [
      {
        step: "Initializing workflow...",
        status: stepsStatus.CONNECTING as StepsStatus,
      },
    ],
    workflowStage: workflowStatus.STARTED as WorkflowStatus,
  },
  inProgress: {
    steps: [
      {
        step: "Step 1: Data Fetching",
        status: stepsStatus.COMPLETED as StepsStatus,
        timeLogged: "1.5s",
      },
      {
        step: "Step 2: Processing Data",
        status: stepsStatus.PROCESSING as StepsStatus,
      },
      {
        step: "Step 3: Awaiting External Service",
        status: stepsStatus.PROCESSING as StepsStatus,
      },
    ],
    workflowStage: workflowStatus.IN_PROGRESS as WorkflowStatus,
  },
  completed: {
    steps: [
      {
        step: "Action A",
        status: stepsStatus.COMPLETED as StepsStatus,
        timeLogged: "0.8s",
      },
      {
        step: "Action B",
        status: stepsStatus.COMPLETED as StepsStatus,
        timeLogged: "2.1s",
      },
    ],
    workflowStage: workflowStatus.COMPLETED as WorkflowStatus,
  },
  failedStep: {
    steps: [
      { step: "Pre-check", status: stepsStatus.COMPLETED as StepsStatus },
      { step: "Critical Operation", status: stepsStatus.FAILED as StepsStatus },
      {
        step: "Post-failure analysis",
        status: stepsStatus.PROCESSING as StepsStatus,
      },
    ],
    workflowStage: workflowStatus.IN_PROGRESS as WorkflowStatus, // Workflow might still be 'in progress' or 'failed'
  },
  failedWorkflow: {
    steps: [
      { step: "Pre-check", status: stepsStatus.COMPLETED as StepsStatus },
      { step: "Critical Operation", status: stepsStatus.FAILED as StepsStatus },
    ],
    workflowStage: workflowStatus.FAILED as WorkflowStatus,
  },
};

const outputChatBubbleData: {
  default: OutputChatBubbleProps;
  multiItem: OutputChatBubbleProps;
  withVideo: OutputChatBubbleProps;
  complexOutput: OutputChatBubbleProps;
  withAudio: OutputChatBubbleProps;
} = {
  default: {
    transition_id: "trans_test_001",
    node_id: "node_test_alpha",
    tool_name: "SimpleScriptTool",
    result: [
      {
        data: "console.log('Test script executed successfully!');",
        data_type: "javascript",
        property_name: "execution_log",
      },
      {
        data: "The script performed a basic console output.",
        data_type: "text",
        property_name: "summary",
      },
    ],
    approval_required: false,
    status: "COMPLETED",
    sequence: 1,
    workflow_status: "COMPLETED",
  },
  multiItem: {
    transition_id: "trans_test_002",
    node_id: "node_test_beta",
    tool_name: "ComplexDataTool",
    result: [
      {
        data: '{\n  "name": "Test Product",\n  "version": "1.2.3",\n  "status": "active"\n}',
        data_type: "json",
        property_name: "product_details",
      },
      {
        data: "User feedback has been processed.",
        data_type: "text",
        property_name: "feedback_summary",
      },
      {
        data: "Next_steps_required: true",
        data_type: "boolean_string",
        property_name: "follow_up_flag",
      },
    ],
    approval_required: true,
    status: "COMPLETED",
    sequence: 2,
    workflow_status: "COMPLETED",
  },
  withVideo: {
    // New data for video test
    transition_id: "trans_test_003",
    node_id: "node_test_gamma",
    tool_name: "VideoOutputTool",
    result: [
      {
        data: "This is a summary of the video output.",
        data_type: "text",
        property_name: "video_summary",
      },
      {
        data: "https://www.w3schools.com/html/mov_bbb.mp4", // Sample video URL
        data_type: "video/mp4",
        property_name: "screen_recording",
      },
      {
        data: "Another text item after the video.",
        data_type: "text",
        property_name: "additional_notes",
      },
    ],
    approval_required: false,
    status: "COMPLETED",
    sequence: 3,
    workflow_status: "COMPLETED",
  },
  complexOutput: {
    transition_id: "trans_test_004",
    node_id: "node_test_delta",
    tool_name: "ComplexOutputTool",
    result: [
      {
        data: [
          {
            data: "https://ciny-dev.s3.amazonaws.com/ciny-dev/videos/thumbnail.jpg",
            data_type: "string",
            property_name: "url",
          },
          {
            data: "image/jpeg",
            data_type: "string",
            property_name: "mimetype",
          },
        ],
        data_type: "object",
        property_name: "thumbnail",
      },
      {
        data: [
          {
            data: "https://ciny-dev.s3.amazonaws.com/ciny-dev/videos/final.mp4",
            data_type: "string",
            property_name: "url",
          },
          {
            data: "video/mp4",
            data_type: "string",
            property_name: "mimetype",
          },
        ],
        data_type: "object",
        property_name: "video_link",
      },
      {
        data: 88.45,
        data_type: "number",
        property_name: "duration",
      },
    ],
    approval_required: true,
    status: "completed",
    sequence: 6,
    workflow_status: "waiting_for_approval",
  },
  withAudio: {
    transition_id: "trans_test_005",
    node_id: "node_test_epsilon",
    tool_name: "AudioOutputTool",
    result: [
      {
        data: "This is a summary of the audio output.",
        data_type: "text",
        property_name: "audio_summary",
      },
      {
        data: [
          {
            data: "https://www2.cs.uic.edu/~i101/SoundFiles/CantinaBand3.wav",
            data_type: "string",
            property_name: "url",
          },
          {
            data: "audio/wav",
            data_type: "string",
            property_name: "mimetype",
          },
        ],
        data_type: "object",
        property_name: "background_music",
      },
      {
        data: "https://www2.cs.uic.edu/~i101/SoundFiles/ImperialMarch60.wav",
        data_type: "audio/wav",
        property_name: "theme_music",
      },
    ],
    approval_required: false,
    status: "completed",
    sequence: 7,
    workflow_status: "completed",
  },
};

// --- Main Test Page Component ---
export default function ChatComponentsTestPage() {
  const [dynamicSteps, setDynamicSteps] = useState<WorkflowStep[]>([
    { step: "Initial Step", status: stepsStatus.PROCESSING as StepsStatus },
  ]);
  const [dynamicStage, setDynamicStage] = useState<WorkflowStatus>(
    workflowStatus.IN_PROGRESS as WorkflowStatus
  );

  useEffect(() => {
    const timeouts: NodeJS.Timeout[] = [];
    if (
      dynamicStage === workflowStatus.IN_PROGRESS &&
      dynamicSteps.length === 1 &&
      dynamicSteps[0].status === stepsStatus.PROCESSING
    ) {
      timeouts.push(
        setTimeout(() => {
          setDynamicSteps((prev) => [
            {
              ...prev[0],
              status: stepsStatus.COMPLETED as StepsStatus,
              timeLogged: "2s",
            },
          ]);
        }, 2000)
      );
      timeouts.push(
        setTimeout(() => {
          setDynamicSteps((prev) => [
            ...prev,
            {
              step: "Second Step",
              status: stepsStatus.PROCESSING as StepsStatus,
            },
          ]);
        }, 3000)
      );
      timeouts.push(
        setTimeout(() => {
          setDynamicSteps((prev) => {
            const newSteps = [...prev];
            newSteps[1] = {
              ...newSteps[1],
              status: stepsStatus.COMPLETED as StepsStatus,
              timeLogged: "1.5s",
            };
            return newSteps;
          });
          setDynamicStage(workflowStatus.COMPLETED as WorkflowStatus);
        }, 4500)
      );
    }
    return () => timeouts.forEach(clearTimeout);
  }, [dynamicSteps, dynamicStage]);

  const resetDynamicTest = () => {
    setDynamicSteps([
      { step: "Initial Step", status: stepsStatus.PROCESSING as StepsStatus },
    ]);
    setDynamicStage(workflowStatus.IN_PROGRESS as WorkflowStatus);
  };

  return (
    <div className="p-6 bg-background text-foreground flex flex-col gap-10 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-brand-primary mb-6 border-b pb-3">
        Chat Component Test Bed
      </h1>

      {/* Section: Normal ChatBubble */}
      <section>
        <h2 className="text-2xl font-semibold mb-4 text-brand-primary-font">
          1. Normal Chat Messages (ChatBubble)
        </h2>
        <div className="space-y-4">
          <div>
            <ChatBubble
              sender={normalUserMessage.sender}
              avatar={normalUserMessage.avatar}
              name={normalUserMessage.name ?? "User"}
            >
              {normalUserMessage.text}
            </ChatBubble>
            <JsonDisplay data={normalUserMessage} title="User Message Props" />
          </div>
          <div>
            <ChatBubble
              sender={normalEmployeeMessage.sender}
              avatar={normalEmployeeMessage.avatar}
              name={normalEmployeeMessage.name ?? "Agent"}
            >
              {normalEmployeeMessage.text}
            </ChatBubble>
            <JsonDisplay
              data={normalEmployeeMessage}
              title="Employee Message Props"
            />
          </div>
          <div>
            <ChatBubble
              sender={longEmployeeMessage.sender}
              avatar={longEmployeeMessage.avatar}
              name={longEmployeeMessage.name ?? "Agent"}
            >
              {longEmployeeMessage.text}
            </ChatBubble>
            <JsonDisplay
              data={longEmployeeMessage}
              title="Long Employee Message Props"
            />
          </div>
        </div>
      </section>

      {/* Section: LoadingBubble */}
      <section>
        <h2 className="text-2xl font-semibold mb-4 text-brand-primary-font">
          2. Loading Indicator (LoadingBubble)
        </h2>
        <ChatBubble
          sender="employee"
          avatar={DUMMY_AGENT_AVATAR}
          name={DUMMY_AGENT_NAME}
        >
          <LoadingBubble />
        </ChatBubble>
        {/* LoadingBubble has no props, so no JsonDisplay needed */}
      </section>

      {/* Section: ThinkingBubble */}
      <section>
        <h2 className="text-2xl font-semibold mb-4 text-brand-primary-font">
          3. Workflow Progress (ThinkingBubble)
        </h2>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Initial Stage</h3>
            <ChatBubble
              sender="employee"
              avatar={DUMMY_AGENT_AVATAR}
              name={DUMMY_AGENT_NAME}
            >
              <ThinkingBubble {...thinkingBubbleData.initial} />
            </ChatBubble>
            <JsonDisplay
              data={thinkingBubbleData.initial}
              title="Initial Stage Props"
            />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">In Progress Stage</h3>
            <ChatBubble
              sender="employee"
              avatar={DUMMY_AGENT_AVATAR}
              name={DUMMY_AGENT_NAME}
            >
              <ThinkingBubble {...thinkingBubbleData.inProgress} />
            </ChatBubble>
            <JsonDisplay
              data={thinkingBubbleData.inProgress}
              title="In Progress Props"
            />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">Completed Workflow</h3>
            <ChatBubble
              sender="employee"
              avatar={DUMMY_AGENT_AVATAR}
              name={DUMMY_AGENT_NAME}
            >
              <ThinkingBubble {...thinkingBubbleData.completed} />
            </ChatBubble>
            <JsonDisplay
              data={thinkingBubbleData.completed}
              title="Completed Workflow Props"
            />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">
              Failed Step in Workflow
            </h3>
            <ChatBubble
              sender="employee"
              avatar={DUMMY_AGENT_AVATAR}
              name={DUMMY_AGENT_NAME}
            >
              <ThinkingBubble {...thinkingBubbleData.failedStep} />
            </ChatBubble>
            <JsonDisplay
              data={thinkingBubbleData.failedStep}
              title="Failed Step Props"
            />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">Failed Workflow</h3>
            <ChatBubble
              sender="employee"
              avatar={DUMMY_AGENT_AVATAR}
              name={DUMMY_AGENT_NAME}
            >
              <ThinkingBubble {...thinkingBubbleData.failedWorkflow} />
            </ChatBubble>
            <JsonDisplay
              data={thinkingBubbleData.failedWorkflow}
              title="Failed Workflow Props"
            />
          </div>
        </div>
      </section>

      {/* Section: Dynamic ThinkingBubble with setTimeout */}
      <section>
        <h2 className="text-2xl font-semibold mb-4 text-brand-primary-font">
          4. Dynamic Workflow Progress (setTimeout)
        </h2>
        <button
          onClick={resetDynamicTest}
          className="mb-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Run/Reset Dynamic Test
        </button>
        <ChatBubble
          sender="employee"
          avatar={DUMMY_AGENT_AVATAR}
          name={DUMMY_AGENT_NAME}
        >
          <ThinkingBubble steps={dynamicSteps} workflowStage={dynamicStage} />
        </ChatBubble>
        <JsonDisplay
          data={{ steps: dynamicSteps, workflowStage: dynamicStage }}
          title="Dynamic Props"
        />
      </section>

      {/* Section: OutputChatBubble */}
      <section>
        <h2 className="text-2xl font-semibold mb-4 text-brand-primary-font">
          5. Workflow Output (OutputChatBubble)
        </h2>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Default Output</h3>
            <ChatBubble
              sender="employee"
              avatar={DUMMY_AGENT_AVATAR}
              name={DUMMY_AGENT_NAME}
            >
              <OutputChatBubble {...outputChatBubbleData.default} />
            </ChatBubble>
            <JsonDisplay
              data={outputChatBubbleData.default}
              title="Default Output Props"
            />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">Multi-Item Output</h3>
            <ChatBubble
              sender="employee"
              avatar={DUMMY_AGENT_AVATAR}
              name={DUMMY_AGENT_NAME}
            >
              <OutputChatBubble {...outputChatBubbleData.multiItem} />
            </ChatBubble>
            <JsonDisplay
              data={outputChatBubbleData.multiItem}
              title="Multi-Item Output Props"
            />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">Output with Video</h3>
            <ChatBubble
              sender="employee"
              avatar={DUMMY_AGENT_AVATAR}
              name={DUMMY_AGENT_NAME}
            >
              <OutputChatBubble {...outputChatBubbleData.withVideo} />
            </ChatBubble>
            <JsonDisplay
              data={outputChatBubbleData.withVideo}
              title="Video Output Props"
            />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">
              Complex Output with Nested Objects
            </h3>
            <ChatBubble
              sender="employee"
              avatar={DUMMY_AGENT_AVATAR}
              name={DUMMY_AGENT_NAME}
            >
              <OutputChatBubble {...outputChatBubbleData.complexOutput} />
            </ChatBubble>
            <JsonDisplay
              data={outputChatBubbleData.complexOutput}
              title="Complex Output Props"
            />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">
              Output with Audio Files
            </h3>
            <ChatBubble
              sender="employee"
              avatar={DUMMY_AGENT_AVATAR}
              name={DUMMY_AGENT_NAME}
            >
              <OutputChatBubble {...outputChatBubbleData.withAudio} />
            </ChatBubble>
            <JsonDisplay
              data={outputChatBubbleData.withAudio}
              title="Audio Output Props"
            />
          </div>
        </div>
      </section>
    </div>
  );
}
