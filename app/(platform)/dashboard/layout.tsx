import { Metadata } from "next";
import { DashboardSidebar } from "./_components/DashboardSidebar";
import { Providers } from "@/lib/providers/Providers";
import { PricingModal } from "@/components/modals/PricingModal";
import { CreateEmployeeModal } from "./_components/CreateEmployeeModal";

// To Show the current page in the browser tab along with application name.
export const metadata: Metadata = {
  title: "Dashboard",
};

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <main className="flex min-h-screen bg-brand-background flex-row overflow-hidden">
      <Providers>
        <DashboardSidebar />
        <div className="w-full">{children}</div>
        <CreateEmployeeModal />
        <PricingModal />
      </Providers>
    </main>
  );
}
