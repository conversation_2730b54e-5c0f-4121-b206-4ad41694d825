"use client";

import { agent<PERSON><PERSON> } from "@/app/api/agent";
import { employeeWindowRoute } from "@/shared/routes";
import { redirect } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { EmptyAgentsPlaceholder } from "@/components/shared/EmptyAgentsPlaceholder";
import { useEffect } from "react";

export default function DashboardPage() {
  // Fetch agents using React Query
  const { data: agentsResponse, isLoading } = useQuery({
    queryKey: ["agents"],
    queryFn: () => agentApi.getAgents(),
  });

  // Handle redirect in useEffect
  useEffect(() => {
    if (!isLoading && agentsResponse?.data?.[0]?.id) {
      redirect(`${employeeWindowRoute}/${agentsResponse.data[0].id}`);
    }
  }, [isLoading, agentsResponse]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="w-full flex flex-col items-center justify-center h-full px-10 py-6">
        <Skeleton className="h-12 w-48" />
      </div>
    );
  }

  // If no agents are found, show a message
  return (
    <div className="w-full flex flex-col items-center justify-center h-full px-10 py-6">
      <EmptyAgentsPlaceholder />
    </div>
  );
}
