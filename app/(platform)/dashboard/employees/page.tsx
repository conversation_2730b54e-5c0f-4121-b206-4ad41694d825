"use client";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { Users, ArchiveX, LoaderIcon } from "lucide-react";
import { AllEmployeesContent } from "./_components/AllEmployeesContent";
import { EmployeeBenchContent } from "./_components/EmployeeBenchContent";
import { SearchBar } from "@/components/shared/SearchBar";
import { useQuery } from "@tanstack/react-query";
import { agentApi } from "@/app/api/agent";
import { AgentBase, PaginationMetadata } from "@/shared/interfaces";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";

export default function EmployeesPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all-employees");
  const [benchEmployees, setBenchEmployees] = useState<AgentBase[]>([]);
  const [agents, setAgents] = useState<AgentBase[]>([]);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [benchCurrentPage, setBenchCurrentPage] = useState(1);
  const [agentsMetadata, setAgentsMetadata] =
    useState<PaginationMetadata | null>(null);
  const [benchMetadata, setBenchMetadata] = useState<PaginationMetadata | null>(
    null
  );
  const pageSize = 10;

  const { data: agentsResponse, isLoading: isLoadingAgents } = useQuery({
    queryKey: ["agents", currentPage, pageSize],
    queryFn: () => agentApi.getAgents(currentPage, pageSize, false),
  });

  const { data: benchEmployeesResponse, isLoading: isLoadingBenchEmployees } =
    useQuery({
      queryKey: ["benched", benchCurrentPage, pageSize],
      queryFn: () => agentApi.getAgents(benchCurrentPage, pageSize, true),
    });

  useEffect(() => {
    if (agentsResponse) {
      setAgents(agentsResponse.data);
      setAgentsMetadata(agentsResponse.metadata);
    }
  }, [agentsResponse]);

  useEffect(() => {
    if (benchEmployeesResponse) {
      setBenchEmployees(benchEmployeesResponse.data);
      setBenchMetadata(benchEmployeesResponse.metadata);
    }
  }, [benchEmployeesResponse]);

  // Filter employees based on active tab and search query
  const filteredEmployees =
    activeTab === "all-employees"
      ? agents.filter((employee) =>
          employee.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : agents;

  const filteredBenchEmployees =
    activeTab === "employee-bench"
      ? benchEmployees.filter((employee) =>
          (employee as { name: string }).name
            .toLowerCase()
            .includes(searchQuery.toLowerCase())
        )
      : benchEmployees;

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Reset search when switching tabs
    setSearchQuery("");
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    if (activeTab === "all-employees") {
      setCurrentPage(page);
    } else {
      setBenchCurrentPage(page);
    }
  };

  // Generate pagination items
  const generatePaginationItems = (metadata: PaginationMetadata | null) => {
    if (!metadata) return [];

    const items = [];
    const { currentPage, totalPages } = metadata;

    // Always show first page
    if (totalPages > 0) {
      items.push(1);
    }

    // Add ellipsis if there's a gap
    if (currentPage > 3) {
      items.push("ellipsis-start");
    }

    // Add pages around current page
    for (
      let i = Math.max(2, currentPage - 1);
      i <= Math.min(totalPages - 1, currentPage + 1);
      i++
    ) {
      if (!items.includes(i)) {
        items.push(i);
      }
    }

    // Add ellipsis if there's a gap
    if (currentPage < totalPages - 2) {
      items.push("ellipsis-end");
    }

    // Always show last page if it's different from first
    if (totalPages > 1) {
      items.push(totalPages);
    }

    return items;
  };

  if (isLoadingAgents || isLoadingBenchEmployees) {
    return (
      <div className="flex flex-col gap-4 items-center justify-center h-screen">
        <LoaderIcon className="w-10 h-10 animate-spin text-brand-primary" />
        <h1 className="text-brand-primary-font text-xl font-semibold text-center">
          Please Wait While we are fetching your agents
        </h1>
      </div>
    );
  }

  const currentMetadata =
    activeTab === "all-employees" ? agentsMetadata : benchMetadata;
  const currentPageNumber =
    activeTab === "all-employees" ? currentPage : benchCurrentPage;

  return (
    <div className="w-full flex flex-col h-full">
      {/* Container for search input and tabs */}
      <div className="bg-brand-card border border-brand-stroke flex flex-col gap-4 p-4 pb-0 sticky top-0 z-20">
        {/* Search input above tabs */}
        <SearchBar onSearch={setSearchQuery} />

        <Tabs
          defaultValue="all-employees"
          className="w-full"
          onValueChange={handleTabChange}
        >
          <TabsList className="w-2/4 justify-start gap-4 bg-transparent px-3">
            <TabsTrigger
              value="all-employees"
              className="flex items-center gap-2 text-brand-primary-font font-medium data-[state=active]:text-brand-primary data-[state=active]:border-b-[1.5px] data-[state=active]:border-brand-primary dark:data-[state=active]:text-brand-primary dark:data-[state=active]:border-b-2 dark:data-[state=active]:border-brand-primary"
            >
              <Users className="w-4 h-4" />
              All employees
            </TabsTrigger>
            <TabsTrigger
              value="employee-bench"
              className="flex items-center gap-2 text-brand-primary-font font-medium data-[state=active]:text-brand-primary data-[state=active]:border-b-[1.5px] data-[state=active]:border-brand-primary dark:data-[state=active]:text-brand-primary dark:data-[state=active]:border-b-2 dark:data-[state=active]:border-brand-primary"
            >
              <ArchiveX className="w-4 h-4" />
              Employee bench
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Tab content with background color */}
      <div className="flex flex-col h-full bg-brand-background rounded-lg p-4">
        <Tabs
          defaultValue="all-employees"
          value={activeTab}
          className="w-full h-full flex flex-col"
        >
          <TabsContent
            value="all-employees"
            className="w-full flex-1 flex flex-col"
          >
            <div className="w-full flex-1">
              <AllEmployeesContent employees={filteredEmployees} />
            </div>

            {/* Pagination for All Employees */}
            {currentMetadata && currentMetadata.totalPages > 1 && (
              <div className="mt-6 flex justify-center">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentMetadata.hasPreviousPage) {
                            handlePageChange(currentPageNumber - 1);
                          }
                        }}
                        className={
                          !currentMetadata.hasPreviousPage
                            ? "pointer-events-none opacity-50"
                            : "cursor-pointer"
                        }
                      />
                    </PaginationItem>

                    {generatePaginationItems(currentMetadata).map(
                      (item, index) => (
                        <PaginationItem key={index}>
                          {typeof item === "string" ? (
                            <PaginationEllipsis />
                          ) : (
                            <PaginationLink
                              href="#"
                              onClick={(e) => {
                                e.preventDefault();
                                handlePageChange(item);
                              }}
                              isActive={item === currentPageNumber}
                              className="cursor-pointer"
                            >
                              {item}
                            </PaginationLink>
                          )}
                        </PaginationItem>
                      )
                    )}

                    <PaginationItem>
                      <PaginationNext
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentMetadata.hasNextPage) {
                            handlePageChange(currentPageNumber + 1);
                          }
                        }}
                        className={
                          !currentMetadata.hasNextPage
                            ? "pointer-events-none opacity-50"
                            : "cursor-pointer"
                        }
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </TabsContent>

          <TabsContent value="employee-bench" className="flex-1 flex flex-col">
            <div className="flex-1">
              <EmployeeBenchContent
                employees={filteredBenchEmployees}
                searchQuery={searchQuery}
              />
            </div>

            {/* Pagination for Employee Bench */}
            {currentMetadata && currentMetadata.totalPages > 1 && (
              <div className="mt-6 flex justify-center">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentMetadata.hasPreviousPage) {
                            handlePageChange(currentPageNumber - 1);
                          }
                        }}
                        className={
                          !currentMetadata.hasPreviousPage
                            ? "pointer-events-none opacity-50"
                            : "cursor-pointer"
                        }
                      />
                    </PaginationItem>

                    {generatePaginationItems(currentMetadata).map(
                      (item, index) => (
                        <PaginationItem key={index}>
                          {typeof item === "string" ? (
                            <PaginationEllipsis />
                          ) : (
                            <PaginationLink
                              href="#"
                              onClick={(e) => {
                                e.preventDefault();
                                handlePageChange(item);
                              }}
                              isActive={item === currentPageNumber}
                              className="cursor-pointer"
                            >
                              {item}
                            </PaginationLink>
                          )}
                        </PaginationItem>
                      )
                    )}

                    <PaginationItem>
                      <PaginationNext
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentMetadata.hasNextPage) {
                            handlePageChange(currentPageNumber + 1);
                          }
                        }}
                        className={
                          !currentMetadata.hasNextPage
                            ? "pointer-events-none opacity-50"
                            : "cursor-pointer"
                        }
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
