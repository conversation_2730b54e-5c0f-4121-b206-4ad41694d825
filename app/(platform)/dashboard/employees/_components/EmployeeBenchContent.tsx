"use client";
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { EmployeeDescriptionCard } from "./EmployeeDescriptionCard";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { emptyBenchContainer } from "@/shared/constants";
import { dashboardRoute, employeeWindowRoute } from "@/shared/routes";
import { CircleCheck, Loader2, LoaderIcon } from "lucide-react";
import { AgentBase } from "@/shared/interfaces";
import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { agentApi } from "@/app/api/agent";
import { useRouter } from "next/navigation";

interface EmployeeBenchContentProps {
  employees: AgentBase[];
  onEmployeesChange?: () => void; // Callback to notify parent component of changes
  searchQuery: string;
}

export const EmployeeBenchContent = ({
  employees: initialEmployees,
  searchQuery,
}: EmployeeBenchContentProps) => {
  // Local state for employees
  const [employees, setEmployees] = useState(initialEmployees);
  const [loading, setLoading] = useState(false);

  const router = useRouter();

  // Update local state when prop changes
  useEffect(() => {
    setEmployees(initialEmployees);
  }, [initialEmployees]);

  const handleRestoreEmployee = async (employee: AgentBase) => {
    setLoading(true);
    const response = await agentApi.updateAgentSettings(employee.id, {
      is_bench_employee: false,
    });

    if (response.success) {
      router.push(dashboardRoute);
    }
  };

  return (
    <div className="w-full">
      {/* Employee cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {employees.map((employee, index) => (
          <Card
            key={index}
            className="px-8 pt-8 pb-4 rounded-sm border-brand-input"
          >
            <div className="flex flex-row items-center gap-4">
              <div className="hover:bg-brand-clicked hover:text-brand-primary p-2 rounded-lg flex flex-col items-center justify-start gap-2  bg-brand-clicked text-brand-primary ">
                <EmployeeAvatar
                  src={employee.avatar}
                  name={employee.name}
                  className="w-14 h-14 rounded-sm"
                />
              </div>
              <div>
                <h1 className="text-brand-primary-font text-lg font-semibold">
                  {employee.name} the {employee.agent_topic_type}
                </h1>
                <p className="text-brand-secondary-font text-sm mt-1">
                  {employee.description}
                </p>
              </div>
            </div>

            <Dialog>
              <DialogTrigger asChild>
                <PrimaryButton className="ml-auto">
                  Restore Employee
                </PrimaryButton>
              </DialogTrigger>
              <DialogContent className="flex flex-col items-center justify-center gap-8 w-full p-6 overflow-hidden bg-background rounded-lg min-w-[950px]">
                <DialogHeader className="flex flex-row items-center justify-center gap-2 text-center">
                  <DialogTitle></DialogTitle>
                  <span className="text-card-foreground font-sans text-lg font-semibold leading-7 text-center font-primary">
                    Are you sure you want to restore this employee,{" "}
                    {employee.name} the {employee.agent_topic_type}, to your
                    workforce?
                  </span>
                </DialogHeader>
                <div className=" mx-auto flex flex-col gap-8">
                  <div className="flex items-center justify-center ">
                    <EmployeeDescriptionCard {...employee} />
                  </div>
                  <div className="flex flex-col md:flex-row w-full flex-1 gap-2">
                    <DialogClose asChild>
                      <SecondaryButton className="flex-1">
                        Cancel
                      </SecondaryButton>
                    </DialogClose>
                    <DialogClose asChild>
                      <PrimaryButton
                        className="flex-1"
                        onClick={() => handleRestoreEmployee(employee)}
                        disabled={loading}
                      >
                        {loading ? (
                          <LoaderIcon className="animate-spin" />
                        ) : (
                          "Yes"
                        )}
                      </PrimaryButton>
                    </DialogClose>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </Card>
        ))}
      </div>
      {employees.length === 0 && searchQuery !== "" && (
        <div className="col-span-full text-center py-10 text-gray-500">
          No employees found matching your search.
        </div>
      )}
      {employees.length === 0 && searchQuery === "" && (
        <div className="max-w-3xl mx-auto py-14 flex flex-col gap-10 w-full justify-center items-center">
          <h2 className="text-brand-primary-font text-xl font-semibold text-center leading-[140%]">
            Welcome to the Employee Bench, where your AI employees are sent when
            they have no work (i.e., when you unpublish them!)
          </h2>
          <Image
            src={emptyBenchContainer}
            alt="Empty Bench Container"
            width={250}
            height={250}
          />
        </div>
      )}
    </div>
  );
};
