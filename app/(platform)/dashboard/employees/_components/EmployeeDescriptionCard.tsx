"use client";

import { Card } from "@/components/ui/card";
import { AgentBase, Employee } from "@/shared/interfaces";
import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { useRouter } from "next/navigation";
import { dashboardRoute } from "@/shared/routes";

export const EmployeeDescriptionCard = (employee: AgentBase) => {
  const router = useRouter();

  const handleEmployeeClick = (id: string) => {
    router.push(`${dashboardRoute}/employee/${id}`);
  };

  return (
    <Card
      className="p-8 flex flex-row items-center gap-4 cursor-pointer hover:opacity-70 transition-opacity duration-200 rounded-sm border-brand-input min-h-[112px]"
      onClick={() => handleEmployeeClick(employee.id)}
    >
      <div className="hover:bg-brand-clicked hover:text-brand-primary p-2 rounded-lg flex flex-col items-center justify-start gap-2  bg-brand-clicked text-brand-primary ">
        <EmployeeAvatar
          src={employee.avatar}
          name={employee.name}
          className="w-14 h-14 rounded-sm"
        />
      </div>
      <div>
        <h1 className="text-brand-primary-font text-lg font-semibold">
          {employee.name} the {employee.agent_topic_type}
        </h1>
        <p className="text-sm text-brand-secondary-font text-wrap break-all">
          {employee.description.length > 50
            ? `${employee.description.substring(0, 50)}...`
            : employee.description}
        </p>
      </div>
    </Card>
  );
};
