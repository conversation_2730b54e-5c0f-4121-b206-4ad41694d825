"use client";
import { EmptyAgentsPlaceholder } from "@/components/shared/EmptyAgentsPlaceholder";
import { EmployeeDescriptionCard } from "./EmployeeDescriptionCard";
import { AgentBase } from "@/shared/interfaces";

interface AllEmployeesContentProps {
  employees: AgentBase[];
}

export const AllEmployeesContent = ({
  employees,
}: AllEmployeesContentProps) => {
  return (
    <div className="w-full ">
      {employees.length > 0 ? (
        // Employee cards (rendered when employees exist)
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {employees.map((employee, index) => (
            <EmployeeDescriptionCard key={index} {...employee} />
          ))}
        </div>
      ) : (
        // Placeholder (rendered when no employees exist)
        <div className="w-full">
          <EmptyAgentsPlaceholder />
        </div>
      )}
    </div>
  );
};
