"use client";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { EmployeeDepartment } from "@/shared/enums";
import {
  ModelProvider,
  OpenAIModelName,
  AnthropicModelName,
  MetaModelName,
} from "@/shared/enums";
import { EmployeeVisibility } from "@/shared/enums";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { EmployeeAvatarSelector } from "./EmployeeAvatarSelector";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { WandSparkles } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { sanitizeString } from "@/services/helper";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import ApiKeySelector from "./ApiKeySelector";
import { useState } from "react";
import { Agent, AgentCoreDetailsUpdatePayload } from "@/shared/interfaces";
import { editEmployeeProfileSchema } from "@/lib/schemas/editEmployee";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";
import { toast } from "sonner";
import { agentApi } from "@/app/api/agent";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const pickedSchema = editEmployeeProfileSchema.pick({
  name: true,
  agent_topic_type: true,
  description: true,
  avatar: true,
  category: true,
  ruh_credentials: true,
  visibility: true,
  model_provider: true,
  model_name: true,
  model_api_key: true,
});

// Apply superRefine for conditional validation
const editProfileSchema = pickedSchema.superRefine((data, ctx) => {
  if (!data.ruh_credentials) {
    if (!data.model_provider) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["model_provider"],
        message:
          "Model provider is required when Ruh credentials are disabled.",
      });
    }
    // For optional strings, !value will catch undefined, null, or empty string ""
    if (!data.model_name) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["model_name"],
        message: "Model name is required when Ruh credentials are disabled.",
      });
    }
    if (!data.model_api_key) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["model_api_key"],
        message: "API key is required when Ruh credentials are disabled.",
      });
    }
  }
});

type EditProfileSchema = z.infer<typeof editProfileSchema>;

export const EditEmployeeProfileForm = ({ agent }: { agent: Agent }) => {
  const queryClient = useQueryClient();
  // Add state to track selected provider
  const [selectedProvider, setSelectedProvider] = useState<ModelProvider>(
    agent.model_provider || ModelProvider.OPENAI
  );

  const { closeModal } = useEmployeeEditStore();

  // Function to get available models for current provider
  const getModelsForProvider = (provider: ModelProvider) => {
    switch (provider) {
      case ModelProvider.OPENAI:
        return Object.values(OpenAIModelName);
      case ModelProvider.ANTHROPIC:
        return Object.values(AnthropicModelName);
      case ModelProvider.META:
        return Object.values(MetaModelName);
      default:
        return [];
    }
  };

  const form = useForm<EditProfileSchema>({
    resolver: zodResolver(editProfileSchema),
    defaultValues: {
      name: agent.name,
      agent_topic_type: agent.agent_topic_type || "",
      description: agent.description,
      avatar: agent.avatar,
      category: agent.category,
      ruh_credentials: agent.ruh_credentials,
      visibility: agent.visibility,
      model_provider: agent.model_provider || ModelProvider.OPENAI,
      model_name: agent.model_name || "",
      model_api_key: agent.model_api_key || "",
    },
  });

  const { mutate: updateCoreDetails, isPending: isUpdatingCoreDetails } =
    useMutation({
      mutationFn: (details: AgentCoreDetailsUpdatePayload) =>
        agentApi.updateAgentCoreDetails(agent.id, details),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["agents"] });
        queryClient.invalidateQueries({ queryKey: ["agent", agent.id] });
        toast.success("Employee profile updated successfully");
        closeModal();
      },
      onError: (error: any) => {
        toast.error(error.message || "Failed to update employee profile");
      },
    });

  const { mutate: toggleVisibility, isPending: isTogglingVisibility } =
    useMutation({
      mutationFn: () => agentApi.toggleAgentVisibility(agent.id),
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: ["agents"] });
        queryClient.invalidateQueries({ queryKey: ["agent", agent.id] });
        // Optimistically update form value
        const currentVisibility = form.getValues("visibility");
        form.setValue(
          "visibility",
          currentVisibility === EmployeeVisibility.PUBLIC
            ? EmployeeVisibility.PRIVATE
            : EmployeeVisibility.PUBLIC,
          {
            shouldValidate: true,
          }
        );
        toast.success("Employee visibility updated successfully");
      },
      onError: (error: any) => {
        toast.error(error.message || "Failed to update employee visibility");
      },
    });

  const onSubmit = async (formData: EditProfileSchema) => {
    try {
      const { visibility, ...payload } = formData;
      const modelFields = !payload.ruh_credentials
        ? {
            model_provider: payload.model_provider,
            model_name: payload.model_name,
            model_api_key: payload.model_api_key,
          }
        : {
            model_provider: null,
            model_name: null,
            model_api_key: null,
          };

      const finalPayload: AgentCoreDetailsUpdatePayload = {
        name: payload.name,
        description: payload.description,
        avatar: payload.avatar,
        category: payload.category,
        agent_topic_type: payload.agent_topic_type,
        ruh_credentials: payload.ruh_credentials,
        ...modelFields,
      };

      await updateCoreDetails(finalPayload);
    } catch (error) {
      toast.error("An unexpected error occurred.");
    }
  };

  return (
    <Form {...form}>
      <div className="flex flex-col h-full gap-10">
        {/* Scrollable Content */}
        <div className="flex-1">
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-1 flex-row p-10 gap-4 w-full h-full overflow-y-auto max-h-[calc(95vh-300px)]"
          >
            {/* Left Section */}
            <div className="flex flex-col items-center gap-10 w-1/4">
              <EmployeeAvatarSelector
                avatar={form.watch("avatar")}
                setAvatar={(avatar) => form.setValue("avatar", avatar)}
              />

              {!agent.is_imported && (
                <div className="flex flex-row gap-2 items-center">
                  <p className="text-sm font-medium text-brand-primary-font">
                    Set Employee Public
                  </p>

                  <Switch
                    checked={
                      form.watch("visibility") === EmployeeVisibility.PUBLIC
                    }
                    onCheckedChange={() => {
                      // No need to manually set form value here as onSuccess will do it
                      toggleVisibility();
                    }}
                    disabled={isTogglingVisibility}
                  />
                </div>
              )}
            </div>
            {/* Right Section */}
            <div className="flex flex-col gap-10 w-3/4">
              <div className="flex flex-row w-full gap-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Employee Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="agent_topic_type"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Employee Role</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Employee Description</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Textarea
                          {...field}
                          className="resize-none h-40 break-all"
                        />

                        <div className="flex justify-end items-center gap-1 absolute bottom-4 right-2">
                          <span className="text-xs text-brand-secondary-font">
                            {field.value.length}/{500}
                          </span>
                          <WandSparkles className="w-3 h-3 text-brand-secondary-font" />
                        </div>
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Employee Category</FormLabel>
                    <FormControl>
                      <Select {...field} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select Category" />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.values(EmployeeDepartment).map(
                            (department) => (
                              <SelectItem key={department} value={department}>
                                {sanitizeString(department)}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                    </FormControl>
                  </FormItem>
                )}
              />

              <h1 className="text-sm font-medium text-brand-secondary">
                Advance Setting
              </h1>

              <div className="flex flex-col gap-2">
                <div className="flex flex-row gap-4">
                  <p className="text-sm font-medium text-brand-primary-font">
                    Use Ruh Pre Configuration
                  </p>
                  <Switch
                    checked={form.watch("ruh_credentials")}
                    onCheckedChange={(checked) =>
                      form.setValue("ruh_credentials", checked, {
                        shouldValidate: true,
                      })
                    }
                  />
                </div>
                <p className="text-xs text-brand-secondary-font">
                  When enabled, Ruh AI will use optimized LLM settings. Turn off
                  to use custom settings.
                </p>
              </div>

              {!form.watch("ruh_credentials") && (
                <div className="flex flex-row gap-4">
                  <FormField
                    control={form.control}
                    name="model_provider"
                    render={({ field }) => (
                      <FormItem className="w-1/3">
                        <FormLabel>Model Provider</FormLabel>
                        <FormControl>
                          <Select
                            {...field}
                            onValueChange={(value) => {
                              field.onChange(value);
                              setSelectedProvider(value as ModelProvider);
                              // Reset model name when provider changes
                              form.setValue("model_name", "");
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select Provider" />
                            </SelectTrigger>
                            <SelectContent>
                              {Object.values(ModelProvider).map((provider) => (
                                <SelectItem key={provider} value={provider}>
                                  {sanitizeString(provider)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="model_name"
                    render={({ field }) => (
                      <FormItem className="w-1/3">
                        <FormLabel>Model Name</FormLabel>
                        <FormControl>
                          <Select
                            {...field}
                            onValueChange={field.onChange}
                            disabled={!selectedProvider}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select Model" />
                            </SelectTrigger>
                            <SelectContent>
                              {getModelsForProvider(selectedProvider).map(
                                (model) => (
                                  <SelectItem key={model} value={model}>
                                    {sanitizeString(model)}
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="model_api_key"
                    render={({ field }) => (
                      <FormItem className="w-1/3">
                        <FormLabel>API Key</FormLabel>
                        <FormControl>
                          <ApiKeySelector
                            onSelect={(keyId) => field.onChange(keyId)}
                            selectedKey={field.value}
                            disabled={!form.watch("model_name")}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>
          </form>
        </div>

        {/* Fixed Bottom Section */}
        <div className="flex w-full justify-end py-2 border-t border-brand-input-color gap-2 pt-4">
          <SecondaryButton className="w-fit px-4" onClick={closeModal}>
            Cancel
          </SecondaryButton>
          <PrimaryButton
            className="w-fit px-4"
            disabled={!form.formState.isValid || isTogglingVisibility}
            onClick={form.handleSubmit(onSubmit)}
            isLoading={isUpdatingCoreDetails || isTogglingVisibility}
          >
            Save Changes
          </PrimaryButton>
        </div>
      </div>
    </Form>
  );
};
