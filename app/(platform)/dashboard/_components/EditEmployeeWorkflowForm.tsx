"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useState } from "react";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { Agent, WorkflowInDB } from "@/shared/interfaces";
import { editEmployeeProfileSchema } from "@/lib/schemas/editEmployee";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";
import { ChevronLeftIcon, PlusIcon, WrenchIcon, Loader2 } from "lucide-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { agentApi } from "@/app/api/agent";
import { toast } from "sonner";
import { workflowApi } from "@/app/api/workflow";
import { EmployeeWorkflowTable } from "./EmployeeWorkflowTable";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const editEmployeeWorkflowSchema = editEmployeeProfileSchema.pick({
  workflow_ids: true,
});

type EditWorkflowSchema = z.infer<typeof editEmployeeWorkflowSchema>;

export const EditEmployeeWorkflowForm = ({ agent }: { agent: Agent }) => {
  const [workflows, setWorkflows] = useState<string[]>(
    agent.workflow_ids || []
  );
  const [openWorkflowTable, setOpenWorkflowTable] = useState(false);
  const { closeModal } = useEmployeeEditStore();
  const queryClient = useQueryClient();

  // Fetch MCP data for the agent's tools
  const { data: workflowData, isLoading } = useQuery({
    queryKey: ["workflows-by-ids", workflows],
    queryFn: () => workflowApi.getWorkflowsByIds(workflows),
    enabled: workflows.length > 0,
  });

  const form = useForm<EditWorkflowSchema>({
    resolver: zodResolver(editEmployeeWorkflowSchema),
    defaultValues: {
      workflow_ids: agent.workflow_ids || [],
    },
  });

  const { mutate: updateAgentWorkflows, isPending: isUpdating } = useMutation({
    mutationFn: (formData: EditWorkflowSchema) =>
      agentApi.updateAgentWorkflows(agent.id, formData),
  });

  const onSubmit = (formData: EditWorkflowSchema) => {
    formData.workflow_ids = workflows;

    updateAgentWorkflows(formData, {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["agents"] });
        queryClient.invalidateQueries({ queryKey: ["agent", agent.id] });
        toast.success("Employee workflows updated successfully");
        closeModal();
      },
      onError: (error) => {
        toast.error(
          `Failed to update employee workflows: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      },
    });
  };

  const handleRemoveWorkflow = (workflowId: string) => {
    setWorkflows((prev) => prev.filter((id) => id !== workflowId));
  };

  return (
    <div className="flex flex-col h-[calc(100vh-200px)]">
      <div className="flex-1 flex flex-col gap-2 h-full justify-between overflow-y-auto p-10">
        {openWorkflowTable ? (
          <div>
            <button
              onClick={() => setOpenWorkflowTable(false)}
              className="flex flex-row gap-2 items-center"
            >
              <ChevronLeftIcon className="w-4 h-4" />
              <p className="text-brand-primary-font text-sm font-medium">
                Add Workflows
              </p>
            </button>
            <EmployeeWorkflowTable
              setWorkflows={setWorkflows}
              initialWorkflowIds={workflows}
            />
          </div>
        ) : (
          <div className="flex flex-col gap-6">
            <div className="flex flex-row gap-2 w-full justify-between">
              <div className="flex flex-col gap-1">
                <h1 className="text-brand-primary-font text-sm font-medium">
                  Employee Workflows
                </h1>
                <p className="text-xs text-brand-secondary-font">
                  Workflows that can enhance an employee's output. You can add
                  more workflows to your employee here.
                </p>
              </div>
              <SecondaryButton
                className="w-40"
                onClick={() => setOpenWorkflowTable(true)}
              >
                <PlusIcon className="w-4 h-4" />
                Add Workflows
              </SecondaryButton>
            </div>
            <div className="flex flex-row flex-wrap gap-4 w-full">
              {isLoading ? (
                <div className="flex items-center justify-center w-full py-8">
                  <Loader2 className="w-6 h-6 animate-spin text-brand-primary" />
                </div>
              ) : workflowData?.workflows &&
                workflowData.workflows.length > 0 ? (
                <UserWorkflowTable
                  workflows={workflowData.workflows}
                  onRemove={handleRemoveWorkflow}
                />
              ) : (
                <p className="text-brand-secondary-font text-sm">
                  No workflows added yet
                </p>
              )}
            </div>
          </div>
        )}

        <p className="text-brand-secondary text-sm underline text-right">
          Explore More on the Marketplace
        </p>
      </div>

      <div className="flex w-full justify-end py-4 px-6 border-t border-brand-input-color gap-2">
        <SecondaryButton className="w-fit px-4" onClick={closeModal}>
          Cancel
        </SecondaryButton>
        <PrimaryButton
          className="w-fit px-4"
          disabled={!form.formState.isValid}
          onClick={form.handleSubmit(onSubmit)}
          isLoading={isUpdating}
        >
          Save Changes
        </PrimaryButton>
      </div>
    </div>
  );
};

interface WorkflowTableProps {
  workflows: WorkflowInDB[];
  onRemove: (workflowId: string) => void;
}

const UserWorkflowTable = ({ workflows, onRemove }: WorkflowTableProps) => {
  return (
    <Table className="border border-brand-input-color rounded-lg ">
      <TableHeader>
        <TableRow>
          <TableHead className="text-brand-primary-font p-4">
            Workflows
          </TableHead>
          <TableHead className="text-brand-primary-font p-4">
            Description
          </TableHead>
          <TableHead className="text-brand-primary-font p-4">
            Category
          </TableHead>
          <TableHead className="text-brand-primary-font text-right p-4">
            Remove Workflows
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {workflows.map((workflow) => (
          <TableRow key={workflow.id} className="hover:bg-brand-card-hover">
            <TableCell className="font-medium text-brand-primary-font p-4">
              {workflow.name}
            </TableCell>
            <TableCell className="text-brand-secondary-font p-4">
              {workflow.description}
            </TableCell>
            <TableCell className="text-brand-secondary-font p-4">
              {workflow.category}
            </TableCell>
            <TableCell className="text-right p-4">
              <SecondaryButton
                className="w-24"
                onClick={() => onRemove(workflow.id)}
              >
                Remove
              </SecondaryButton>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
