import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { PlusIcon } from "lucide-react";
import EmployeeDialog from "./EmployeeDetailsDialog";
import { AddEmployeeCardProps } from "@/shared/interfaces";
import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { employeeCarouselImages } from "@/shared/constants";

export const EmployeeCard = ({
  name,
  designation,
  description,
  image,
}: AddEmployeeCardProps) => {
  return (
    <div className="flex flex-col gap-2 rounded-sm bg-brand-card p-4 max-w-[335px]">
      <EmployeeAvatar src={image} />
      <h1 className="text-brand-primary-font text-lg font-semibold">
        {name} the {designation}
      </h1>
      <p className="text-brand-secondary-font text-sm">{description}</p>
      <div className="flex flex-row w-full gap-4">
        <EmployeeDialog
          employee={{
            name,
            designation,
            description,
            image,
            carouselImages: employeeCarouselImages,
          }}
        />
        <PrimaryButton className="flex-1">
          <PlusIcon className="h-4 w-4" />
          Add Employee
        </PrimaryButton>
      </div>
    </div>
  );
};
