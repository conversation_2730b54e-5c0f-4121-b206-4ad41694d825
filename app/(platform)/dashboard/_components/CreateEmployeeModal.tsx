"use client";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { CreateEmployeeProfileForm } from "./CreateEmployeeProfileForm";
import { CreateEmployeeGuidelineForm } from "./CreateEmployeeGuidelineForm";
import { CreateEmployeeKnowledgeForm } from "./CreateEmployeeKnowledgeForm";
import { CreateEmployeeToolsForm } from "./CreateEmployeeToolsForm";
import { CreateEmployeeWorkflowForm } from "./CreateEmployeeWorkflowForm";

const steps = [
  {
    step: 1,
    title: "Profile",
  },
  {
    step: 2,
    title: "Guideline",
  },
  {
    step: 3,
    title: "Knowledge",
  },
  {
    step: 4,
    title: "Tools",
  },
  {
    step: 5,
    title: "Workflow",
  },
];

export const CreateEmployeeModal = () => {
  const { isModalOpen, openModal, closeModal, formStep } =
    useEmployeeCreateStore();

  return (
    <Dialog open={isModalOpen} onOpenChange={closeModal}>
      <DialogContent className="min-w-[95vw] h-[95vh] font-primary overflow-hidden">
        <div className="flex flex-col h-full">
          <DialogHeader className="px-6 py-4 flex-none">
            <DialogTitle className="text-brand-primary">
              Create Employee
            </DialogTitle>
          </DialogHeader>

          <div className="flex flex-col flex-1 px-6 overflow-hidden">
            <div className="flex w-full justify-evenly border-b border-brand-input-color pb-4 flex-none">
              {steps.map((step) => (
                <h1
                  key={step.step}
                  className={`text-sm font-medium  ${
                    formStep >= step.step
                      ? "text-brand-primary"
                      : "text-brand-primary-font"
                  }`}
                >
                  Employee {step.title}
                </h1>
              ))}
            </div>

            <div className="flex-1 p-10 h-full">
              {formStep === 1 && <CreateEmployeeProfileForm />}
              {formStep === 2 && <CreateEmployeeGuidelineForm />}
              {formStep === 3 && <CreateEmployeeKnowledgeForm />}
              {formStep === 4 && <CreateEmployeeToolsForm />}
              {formStep === 5 && <CreateEmployeeWorkflowForm />}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
