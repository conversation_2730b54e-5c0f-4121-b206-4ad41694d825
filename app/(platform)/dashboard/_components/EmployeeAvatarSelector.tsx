"use client";

import { CameraIcon } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { agentApi } from "@/app/api/agent";
import { Skeleton } from "@/components/ui/skeleton";
import { AgentAvatarInDB } from "@/shared/interfaces";

export const EmployeeAvatarSelector = ({
  avatar,
  setAvatar,
}: {
  avatar: string | null;
  setAvatar: (avatar: string) => void;
}) => {
  const [open, setOpen] = useState(false);

  const {
    data: avatarsData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["agentAvatars"],
    queryFn: agentApi.listAgentAvatars,
  });

  useEffect(() => {
    if (
      !avatar &&
      !isLoading &&
      !isError &&
      avatarsData?.avatars &&
      avatarsData.avatars.length > 0
    ) {
      setAvatar(avatarsData.avatars[0].url);
    }
  }, [avatar, avatarsData, isLoading, isError, setAvatar]);

  const handleAvatarClick = (imageUrl: string) => {
    setAvatar(imageUrl);
    setOpen(false);
  };

  return (
    <div className="flex flex-col items-center gap-2">
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <button
            type="button"
            className="w-36 h-36 bg-brand-background border border-brand-border-color rounded-full flex items-center justify-center cursor-pointer hover:bg-brand-card/80 transition-colors"
          >
            {avatar ? (
              <img
                src={avatar}
                alt="Selected Avatar"
                className="w-full h-full object-cover rounded-full"
              />
            ) : (
              <CameraIcon className="w-10 h-10 text-brand-secondary" />
            )}
          </button>
        </DialogTrigger>
        <DialogContent className="">
          <DialogHeader>
            <DialogTitle className="font-primary text-brand-primary-font text-lg font-medium ">
              Choose Avatar
            </DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4 py-4 max-h-96 overflow-y-auto">
            {isLoading &&
              Array.from({ length: 6 }).map((_, index) => (
                <Skeleton key={index} className="w-20 h-20 rounded-full" />
              ))}
            {isError && (
              <p className="col-span-full text-center text-red-500">
                Failed to load avatars. Please try again.
              </p>
            )}
            {!isLoading &&
              !isError &&
              avatarsData?.avatars.map((avatarItem: AgentAvatarInDB) => (
                <button
                  key={avatarItem.id}
                  className="w-20 h-20 focus:outline-none focus:ring-2 focus:ring-brand-primary focus:ring-offset-2 rounded-full"
                  onClick={() => handleAvatarClick(avatarItem.url)}
                >
                  <img
                    src={avatarItem.url}
                    alt={`Avatar ${avatarItem.id}`}
                    className="w-full h-full object-cover rounded-full transition-transform hover:scale-105"
                  />
                </button>
              ))}
          </div>
        </DialogContent>
      </Dialog>

      <p className="font-primary text-brand-secondary-font text-xs font-medium text-center">
        Click to Choose Avatar
      </p>
    </div>
  );
};
