"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { settingsButtons, userSettingsButtons } from "@/shared/constants";

export const UserSettingsSidebarButtons = () => {
  const pathname = usePathname();

  return (
    <div className="flex flex-col gap-2 w-full">
      {userSettingsButtons.map((button, index) => (
        <Link
          key={index}
          href={button.path}
          className={`flex items-center gap-3 p-1 rounded-md transition-colors ${
            pathname === button.path
              ? "bg-brand-clicked text-brand-primary font-semibold"
              : "hover:bg-brand-clicked hover:text-brand-primary text-brand-primary-font"
          }`}
        >
          <div
            className={`flex items-center justify-center w-6 h-6 ${
              pathname === button.path
                ? "text-brand-primary"
                : "text-brand-primary-font"
            }`}
          >
            <button.icon className="w-5 h-5" strokeWidth={1.5} />
          </div>
          <span className="text-sm font-medium">{button.text}</span>
        </Link>
      ))}
    </div>
  );
};
