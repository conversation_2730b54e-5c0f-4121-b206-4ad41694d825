"use client";

import { useState } from "react";
import Image from "next/image";
import { Circle<PERSON>lus, Trash2 } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ConnectedApp } from "@/shared/interfaces";
import { connectedAppsData } from "@/shared/constants";

// Initial dummy data - empty array as in your original code
const initialConnectedApps: ConnectedApp[] = [];

// Available apps that can be connected
const availableApps: ConnectedApp[] = connectedAppsData;

export const ConnectedAppsSection = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [connectedApps, setConnectedApps] =
    useState<ConnectedApp[]>(initialConnectedApps);

  const handleConnect = (app: ConnectedApp) => {
    // Check if app is already connected
    const isAlreadyConnected = connectedApps.some(
      (connectedApp) => connectedApp.id === app.id
    );

    if (isAlreadyConnected) {
      return; // Skip if already connected
    }

    // Add app to connected apps
    setConnectedApps((prev) => [...prev, { ...app, connected: true }]);

    // Close dialog after a brief delay to show the button state change
    setTimeout(() => {
      setIsDialogOpen(false);
    }, 500);
  };

  const handleDisconnect = (appId: string) => {
    setConnectedApps((prev) => prev.filter((app) => app.id !== appId));
  };

  return (
    <div className="w-full flex flex-col gap-4">
      <div className="text-brand-primary-font flex justify-between items-center">
        <h1 className="text-base text-gradient-brand">Connected Apps</h1>
        <CirclePlus
          className="cursor-pointer w-5 h-5 text-brand-secondary"
          strokeWidth={2}
          onClick={() => setIsDialogOpen(true)}
        />
      </div>

      <div className="flex flex-col gap-2">
        {connectedApps.length === 0 ? (
          <p className="text-sm text-brand-secondary-font text-center">
            Connect To External Communication Apps Here (Gmail, Telegram, etc.)
          </p>
        ) : (
          connectedApps.map((app) => (
            <div
              key={app.id}
              className="flex items-center justify-between gap-2 p-2 rounded-md group hover:bg-gray-100"
            >
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 relative">
                  <Image
                    src={app.icon}
                    alt={app.name}
                    fill
                    className="object-contain"
                  />
                </div>
                <span className="text-sm text-brand-primary-font">
                  {app.name}
                </span>
              </div>
              <Trash2
                className="w-4 h-4 text-red-500 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => handleDisconnect(app.id)}
              />
            </div>
          ))
        )}
      </div>

      {/* Modal Dialog for adding new apps */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md bg-brand-card">
          <DialogHeader>
            <DialogTitle>Apps</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground mb-4">
              Connect to any of the communication apps from the below list:
            </p>
            <div className="flex flex-col gap-4">
              {availableApps.map((app) => (
                <div key={app.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 relative">
                      <Image
                        src={app.icon}
                        alt={app.name}
                        fill
                        className="object-contain"
                      />
                    </div>
                    <span>{app.name}</span>
                  </div>
                  <Button
                    onClick={() => handleConnect(app)}
                    className="bg-brand-primary text-white hover:bg-brand-primary/70"
                    disabled={connectedApps.some(
                      (connectedApp) => connectedApp.id === app.id
                    )}
                  >
                    {connectedApps.some(
                      (connectedApp) => connectedApp.id === app.id
                    )
                      ? "Connected"
                      : "Connect"}
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
