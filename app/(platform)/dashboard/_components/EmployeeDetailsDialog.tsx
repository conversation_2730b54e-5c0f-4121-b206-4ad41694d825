import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import DialogCarousel from "@/components/shared/DialogCarousel";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { EmployeeDialogProps } from "@/shared/interfaces";
import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";

const EmployeeDialog = ({ employee }: EmployeeDialogProps) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <SecondaryButton className="flex-1">View</SecondaryButton>
      </DialogTrigger>
      <DialogContent className="flex flex-col items-center w-full  p-6 overflow-hidden bg-background rounded-lg">
        <DialogHeader className="flex flex-row items-center justify-center gap-2  text-center">
          <DialogTitle></DialogTitle>
          <EmployeeAvatar src={employee.image} className="w-10 h-10" />
          <h1 className="text-brand-primary-font text-xl font-semibold">
            {employee.name} the {employee.designation}
          </h1>
        </DialogHeader>

        <div className="text-center w-[90%]">
          <p className="text-sm text-brand-secondary-font ">
            {employee.description}
          </p>
        </div>

        <p className="text-brand-primary-font text-sm ">
          How to Use This Employee?
        </p>

        <div className="relative">
          <DialogCarousel images={employee.carouselImages} />
        </div>

        <p className="text-brand-primary-font text-sm font-semibold">
          Add the employee to your employee list
        </p>

        <PrimaryButton>Add Employee</PrimaryButton>
      </DialogContent>
    </Dialog>
  );
};

export default EmployeeDialog;
