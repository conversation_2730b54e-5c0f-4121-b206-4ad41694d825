"use client";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Textarea } from "@/components/ui/textarea";
import { WandSparkles } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { sanitizeString } from "@/services/helper";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { EmployeeTone } from "@/shared/enums";
import { editEmployeeProfileSchema } from "@/lib/schemas/editEmployee";
import { Agent, AgentCoreDetailsUpdatePayload } from "@/shared/interfaces";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { agentApi } from "@/app/api/agent";
import { toast } from "sonner";

const editGuidelineSchema = editEmployeeProfileSchema.pick({
  system_message: true,
  tone: true,
});

type EditGuidelineSchema = z.infer<typeof editGuidelineSchema>;

export const EditEmployeeGuidelineForm = ({ agent }: { agent: Agent }) => {
  const { closeModal } = useEmployeeEditStore();
  const queryClient = useQueryClient();

  const form = useForm<EditGuidelineSchema>({
    resolver: zodResolver(editGuidelineSchema),
    defaultValues: {
      system_message: agent.system_message,
      tone: agent.tone,
    },
  });

  const { mutate: updateGuidelines, isPending: isUpdatingGuidelines } =
    useMutation({
      mutationFn: (details: AgentCoreDetailsUpdatePayload) =>
        agentApi.updateAgentCoreDetails(agent.id, details),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["agents"] });
        queryClient.invalidateQueries({ queryKey: ["agent", agent.id] });
        toast.success("Employee guidelines updated successfully");
        closeModal();
      },
      onError: (error: any) => {
        toast.error(error.message || "Failed to update employee guidelines");
      },
    });

  const onSubmit = (formData: EditGuidelineSchema) => {
    updateGuidelines(formData);
  };

  return (
    <Form {...form}>
      <div className="flex flex-col h-full p-4">
        {/* Scrollable Content */}
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col p-10 gap-10 w-full h-full overflow-y-auto max-h-[calc(95vh-300px)]"
        >
          <FormField
            control={form.control}
            name="system_message"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel>Instructions for the Employee</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Textarea
                      {...field}
                      className="resize-none h-80"
                      placeholder="In-Depth Task List and Description Here of the Tasks and Goals the Employee Can Execute"
                    />

                    <div className="flex justify-end items-center gap-1 absolute bottom-4 right-2">
                      <span className="text-xs text-brand-secondary-font">
                        {field.value.length}/{1500}
                      </span>
                      <WandSparkles className="w-3 h-3 text-brand-secondary-font" />
                    </div>
                  </div>
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="tone"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel>Employee Tone</FormLabel>
                <FormControl>
                  <Select {...field} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Tone" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(EmployeeTone).map((tone) => (
                        <SelectItem key={tone} value={tone}>
                          {sanitizeString(tone)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
              </FormItem>
            )}
          />
        </form>

        {/* Fixed Bottom Section */}
        <div className="flex w-full justify-end py-2 border-t border-brand-input-color gap-2 pt-4">
          <SecondaryButton className="w-fit px-4" onClick={closeModal}>
            Cancel
          </SecondaryButton>
          <PrimaryButton
            className="w-fit px-4"
            disabled={!form.formState.isValid}
            onClick={form.handleSubmit(onSubmit)}
            isLoading={isUpdatingGuidelines}
          >
            Save Changes
          </PrimaryButton>
        </div>
      </div>
    </Form>
  );
};
