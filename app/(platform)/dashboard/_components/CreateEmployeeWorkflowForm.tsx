"use client";

import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { createEmployeeProfileSchema } from "@/lib/schemas/createEmployee";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useState, useEffect } from "react";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { EmployeeWorkflowTable } from "./EmployeeWorkflowTable";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { agentApi } from "@/app/api/agent";
import { toast } from "sonner";
import {
  OpenAIModelName,
  AnthropicModelName,
  MetaModelName,
  EmployeeDepartment,
} from "@/shared/enums";

const createWorkflowSchema = createEmployeeProfileSchema.pick({
  workflow_ids: true,
});

type CreateWorkflowSchema = z.infer<typeof createWorkflowSchema>;

export const CreateEmployeeWorkflowForm = () => {
  const [workflows, setWorkflows] = useState<string[]>([]);
  const { setData, closeModal, formStep, setFormStep, data } =
    useEmployeeCreateStore();
  const queryClient = useQueryClient();

  const form = useForm<CreateWorkflowSchema>({
    resolver: zodResolver(createWorkflowSchema),
    defaultValues: {
      workflow_ids: data.workflow_ids || [],
    },
  });

  // Update form values when workflows change
  useEffect(() => {
    form.setValue("workflow_ids", workflows);
  }, [workflows, form]);

  const { mutate: createAgent, isPending: isCreatingAgent } = useMutation({
    mutationFn: agentApi.createAgent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["agents"] });
      closeModal();
      useEmployeeCreateStore.getState().reset();
      toast.success("Agent created successfully");
    },
    onError: (error) => {
      toast.error(
        `Failed to create agent: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    },
  });

  const onSubmit = (formData: CreateWorkflowSchema) => {
    // Combine existing data with the new workflow_ids and required fields
    const agentCreateData = {
      ...data,
      model_provider: data.model_provider ?? null,
      model_name:
        (data.model_name as
          | OpenAIModelName
          | AnthropicModelName
          | MetaModelName) ?? null,
      model_api_key: data.model_api_key ?? null,
      workflow_ids: workflows,
      mcp_server_ids: data.mcp_server_ids ?? null,
      files: data.files ?? null,
      urls: data.urls ?? null,
      status: "active",
      department: EmployeeDepartment.CUSTOMER_SUPPORT,
      is_imported: false,
    };

    createAgent(agentCreateData);
  };

  return (
    <div className="h-full flex flex-col justify-between">
      <div className="h-full flex flex-col justify-between p-10 overflow-y-auto max-h-[calc(95vh-300px)]">
        <EmployeeWorkflowTable
          setWorkflows={setWorkflows}
          initialWorkflowIds={data.workflow_ids ?? []}
        />
        <p className="text-brand-secondary text-sm underline text-right">
          Explore More on the Marketplace
        </p>
      </div>

      {/* Fixed Bottom Section */}
      <div className="flex w-full justify-between py-2 border-t border-brand-input-color flex-none">
        <SecondaryButton className="w-fit px-4" onClick={closeModal}>
          Cancel
        </SecondaryButton>
        <div className="flex gap-2">
          <SecondaryButton
            className="w-fit px-4"
            onClick={() => setFormStep(formStep - 1)}
          >
            Back
          </SecondaryButton>
          <PrimaryButton
            className="w-fit px-4"
            disabled={isCreatingAgent}
            onClick={form.handleSubmit(onSubmit)}
          >
            {isCreatingAgent ? "Creating..." : "Create"}
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};
