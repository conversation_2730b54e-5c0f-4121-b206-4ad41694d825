"use client";

import { List, LayoutGrid, UserPlus, SquarePenIcon } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { CreateEmployeeModal } from "@/app/(platform)/dashboard/_components/CreateEmployeeModal";
import { Agent } from "@/shared/interfaces";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { employeeWindowRoute } from "@/shared/routes";
import { useQuery } from "@tanstack/react-query";
import { agentApi } from "@/app/api/agent";
import { Skeleton } from "@/components/ui/skeleton";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";

// Loading skeleton components
const CardSkeleton = () => (
  <div className="flex flex-col gap-2 items-center justify-center">
    <div className="hover:bg-brand-clicked p-2 w-full px-0 rounded-sm flex flex-col items-center justify-start gap-2 max-h-[145px]">
      <Skeleton className="w-20 h-20 rounded-sm" />
      <Skeleton className="h-4 w-24" />
    </div>
  </div>
);

const ListSkeleton = () => (
  <div className="flex flex-row gap-2 w-full justify-between">
    <div className="flex-1 hover:bg-brand-clicked p-1 rounded-sm flex items-center gap-2">
      <Skeleton className="h-8 w-8 rounded-full" />
      <Skeleton className="h-4 w-32" />
    </div>
  </div>
);

export const EmployeeSection = () => {
  const pathname = usePathname();
  const { openModal } = useEmployeeCreateStore();

  const { data: agentsResponse, isLoading } = useQuery({
    queryKey: ["agents"],
    queryFn: () => agentApi.getAgents(),
  });

  // Use agents directly from the API response
  const agents = agentsResponse?.data || [];

  return (
    <div className="w-full flex flex-col gap-4 relative ">
      <div className="text-brand-primary-font flex justify-between items-center">
        <h1 className="text-base">Your Employees</h1>
        <SquarePenIcon className="cursor-pointer w-5 h-5" strokeWidth={2} />
      </div>
      <div className="flex flex-col gap-2">
        {(!agents || agents.length === 0) && !isLoading && (
          <p className="text-sm text-brand-secondary-font text-center ">
            All The New AI Employees You Add/Create Will Be Visible Here
          </p>
        )}

        <Tabs defaultValue="cards">
          <TabsList className="w-full justify-start gap-4 bg-transparent">
            <TabsTrigger
              value="cards"
              className="flex items-center gap-2 data-[state=active]:text-brand-primary data-[state=active]:border-b-1 "
            >
              <LayoutGrid className="w-4 h-4" />
              Cards
            </TabsTrigger>
            <TabsTrigger
              value="list"
              className="flex items-center gap-2 data-[state=active]:text-brand-primary data-[state=active]:border-b-1 "
            >
              <List className="w-4 h-4" />
              List
            </TabsTrigger>
          </TabsList>

          <TabsContent value="cards">
            <div className="grid grid-cols-2 gap-2 pt-4">
              {isLoading ? (
                <>
                  <CardSkeleton />
                  <CardSkeleton />
                  <CardSkeleton />
                  <CardSkeleton />
                </>
              ) : (
                <>
                  {agents.map((agent, index) => (
                    <EmployeeCardStyle
                      key={agent.id?.toString() || index}
                      agent={agent}
                      pathname={pathname}
                    />
                  ))}
                </>
              )}
            </div>
          </TabsContent>

          <TabsContent value="list">
            <div className="flex flex-col gap-2 pt-4">
              {isLoading ? (
                <>
                  <ListSkeleton />
                  <ListSkeleton />
                  <ListSkeleton />
                  <ListSkeleton />
                </>
              ) : (
                <>
                  {agents.map((agent, index) => (
                    <EmployeeListStyle
                      key={agent.id?.toString() || index}
                      agent={agent}
                      pathname={pathname}
                    />
                  ))}
                </>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
      <div className="sticky bottom-0 left-0 right-0 bg-brand-card p-2  z-10">
        <button
          className="w-full bg-brand-primary hover:bg-brand-primary-dark text-white p-2 rounded-lg flex items-center gap-2 justify-center"
          onClick={openModal}
        >
          <UserPlus className="w-5 h-5" />
          <p className="text-sm font-semibold">Create New Employee</p>
        </button>
      </div>
      <CreateEmployeeModal />
    </div>
  );
};

// Updated to pass agent name to EmployeeAvatar
const EmployeeListStyle = ({
  agent,
  pathname,
}: {
  agent: Agent;
  pathname: string;
}) => {
  const agentIdStr = agent.id?.toString() || "";
  const isActive = agentIdStr ? pathname.includes(agentIdStr) : false;

  return (
    <div className="flex flex-row gap-2 w-full justify-between">
      <Link
        href={`${employeeWindowRoute}/${agentIdStr}`}
        className={`flex-1 hover:bg-brand-clicked hover:text-brand-primary p-1 rounded-sm flex items-center gap-2 ${
          isActive ? "bg-brand-clicked text-brand-primary" : ""
        }`}
      >
        <EmployeeAvatar src={agent.avatar} name={agent.name} />
        <p className="text-sm font-semibold">
          {agent.agent_topic_type || "AI Agent"}
        </p>
      </Link>
    </div>
  );
};

// Updated to pass agent name to EmployeeAvatar
const EmployeeCardStyle = ({
  agent,
  pathname,
}: {
  agent: Agent;
  pathname: string;
}) => {
  const agentIdStr = agent.id?.toString() || "";

  return (
    <div className="flex flex-col gap-2 items-center justify-center h-36">
      <Link
        href={`${employeeWindowRoute}/${agentIdStr}`}
        className={`hover:bg-brand-clicked hover:text-brand-primary p-2 w-full px-0 rounded-sm flex flex-col items-center justify-start gap-2 flex-grow  ${
          agentIdStr && pathname.includes(agentIdStr)
            ? "bg-brand-clicked text-brand-primary "
            : ""
        }`}
      >
        <div className="relative w-20 h-20">
          <EmployeeAvatar
            src={agent.avatar}
            name={agent.name}
            className="w-full h-full rounded-sm"
          />
        </div>
        <p className="text-sm text-center text-wrap max-w-[90%] truncate">
          {agent.agent_topic_type || "AI Agent"}
        </p>
      </Link>
    </div>
  );
};
