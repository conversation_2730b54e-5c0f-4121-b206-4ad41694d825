"use client";

import * as React from "react";
import { usePathname } from "next/navigation";
import { PanelLeftCloseIcon } from "lucide-react";
import { toast } from "sonner";
import { CustomSeparator } from "@/components/shared/CustomSeparator";
import { LeftBar } from "./LeftBar";
import { CreditsBox } from "./CreditsBox";
import { EmployeeSection } from "./EmployeeSection";
import { ConnectedAppsSection } from "./ConnectedAppsSection";
import { EmployeeSidebarButtons } from "./EmployeeSidebarButtons";
import { SettingsSidebarButtons } from "./SettingsSidebarButtons";
import {
  dashboardRoute,
  employeeWindowRoute,
  employeesRoute,
  settingsRoute,
  userSettingsRoute,
} from "@/shared/routes";
import api from "@/services/axios";
import { NotificationSection } from "./NotificationSection";
import { useNotificationStore } from "@/hooks/use-notification";
import { UserSettingsSidebarButtons } from "./UserSettingsSidebarButtons";

export function DashboardSidebar() {
  const [secondarySidebarOpen, setSecondarySidebarOpen] = React.useState(true);
  const pathname = usePathname();
  const { isNotificationOpen, closeNotification } = useNotificationStore();

  // Effect to open sidebar when notification panel is shown
  React.useEffect(() => {
    if (isNotificationOpen && !secondarySidebarOpen) {
      setSecondarySidebarOpen(true);
    }
  }, [isNotificationOpen, secondarySidebarOpen]);

  // Checking Invitation
  React.useEffect(() => {
    const acceptInvitation = async () => {
      try {
        if (typeof window !== "undefined") {
          const tokenVal = localStorage.getItem("inviteToken");
          if (tokenVal) {
            await api.post(`/organizations/accept-invite?token=${tokenVal}`);
            toast.success("Invitation accepted successfully");
            localStorage.removeItem("inviteToken");
          }
        }
      } catch (error) {
        console.error("error=>", error);
        toast.error("Failed to accept invitation");
      }
    };

    acceptInvitation();
  }, []);

  const toggleSecondarySidebar = () => {
    setSecondarySidebarOpen(!secondarySidebarOpen);
    // Close notification view when sidebar is closed
    if (secondarySidebarOpen) {
      closeNotification();
    }
  };

  // Check which page we're on
  const isEmployeesPage = pathname.startsWith(employeesRoute);
  const isSettingsPage = pathname.startsWith(settingsRoute);
  const isUserSettingsPage = pathname.startsWith(userSettingsRoute);
  const isHomePage =
    pathname === dashboardRoute ||
    pathname.startsWith(employeeWindowRoute + "/");

  return (
    <div className="flex flex-row [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]  ">
      <LeftBar
        isSecondaryBarOpen={secondarySidebarOpen}
        toggleSecondaryBar={toggleSecondarySidebar}
      />
      <div
        className={`flex sticky top-0 bg-brand-card border-r border-brand-stroke transition-all duration-200 ease-in-out h-screen ${
          secondarySidebarOpen
            ? "w-[280px] opacity-100 p-4"
            : "w-0 p-0 opacity-0 overflow-hidden"
        }`}
      >
        <div className="flex flex-col gap-4  h-full w-full">
          <div className="flex flex-col gap-4">
            <div className="flex flex-row justify-between w-full font-primary py-2 ">
              <div className="flex flex-col">
                <h1 className="text-brand-primary-font font-semibold text-sm">
                  RUH AI
                </h1>
                <p className="text-brand-secondary-font text-sm">Enterprise</p>
              </div>
              <div className="flex h-full items-end">
                <PanelLeftCloseIcon
                  onClick={toggleSecondarySidebar}
                  strokeWidth={1.2}
                  className="cursor-pointer"
                />
              </div>
            </div>
            <CustomSeparator />
          </div>

          <div className="flex-1 overflow-y-auto py-4 space-y-8 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
            {isNotificationOpen ? (
              <NotificationSection />
            ) : (
              <>
                {isHomePage && (
                  <div className="flex flex-col h-full justify-between w-full gap-12">
                    <EmployeeSection />
                    {/* <ConnectedAppsSection /> */}
                  </div>
                )}

                {isEmployeesPage && (
                  <div className="flex flex-col w-full gap-8">
                    <EmployeeSidebarButtons />
                  </div>
                )}

                {isSettingsPage && (
                  <div className="flex flex-col w-full gap-8">
                    <SettingsSidebarButtons />
                  </div>
                )}

                {isUserSettingsPage && (
                  <div className="flex flex-col w-full gap-8">
                    <UserSettingsSidebarButtons />
                  </div>
                )}
              </>
            )}
          </div>

          <div className="w-full flex flex-col gap-4 pt-6">
            <CustomSeparator />
            <CreditsBox />
          </div>
        </div>
      </div>
    </div>
  );
}
