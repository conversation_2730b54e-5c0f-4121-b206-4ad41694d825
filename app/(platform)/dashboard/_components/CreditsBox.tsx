"use client";

import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { Progress } from "@/components/ui/progress";
import { AlertCircle, RocketIcon } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { usePricingModalStore } from "@/hooks/use-pricing";

export const CreditsBox = () => {
  const { isOpen, openModal, closeModal } = usePricingModalStore();

  return (
    <div className="bg-brand-card w-full flex flex-col gap-2 pt-4">
      <div className="flex justify-between text-brand-secondary-font text-sm">
        <p>Credits Remaining</p>
        <div className="flex items-center gap-1">
          <p>400/600</p>
          <Tooltip>
            <TooltipTrigger>
              <AlertCircle className="h-4 w-4 " />
            </TooltipTrigger>
            <TooltipContent className="max-w-[250px]">
              This bar represents the total credits you have remaining in the
              plan tier you're using.
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
      <Progress value={43} />
      <PrimaryButton className="brand-gradient-indicator" onClick={openModal}>
        <RocketIcon />
        Upgrade
      </PrimaryButton>
    </div>
  );
};
