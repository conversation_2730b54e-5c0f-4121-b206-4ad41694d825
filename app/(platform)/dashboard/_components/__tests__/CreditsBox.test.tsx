import { render, screen } from "@testing-library/react";
import { CreditsBox } from "../CreditsBox";

describe("CreditsBox", () => {
  it("renders credits label and count", () => {
    render(<CreditsBox />);
    expect(screen.getByText(/credits remaining/i)).toBeInTheDocument();
    expect(screen.getByText(/\d+\/\d+/)).toBeInTheDocument();
  });

  it("renders the progress bar", () => {
    render(<CreditsBox />);
    const progress = screen.getByRole("progressbar");
    expect(progress).toBeInTheDocument();
  });

  it("renders Upgrade button with Rocket icon (svg)", () => {
    render(<CreditsBox />);
    const button = screen.getByRole("button", { name: /upgrade/i });
    expect(button).toBeInTheDocument();
    expect(button.querySelector("svg")).toBeInTheDocument(); // simple and flexible
  });
});
