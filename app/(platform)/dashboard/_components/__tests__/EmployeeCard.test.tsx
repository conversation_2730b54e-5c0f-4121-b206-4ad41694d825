import { render, screen, fireEvent } from "@testing-library/react";
import { EmployeeCard } from "../EmployeeCard";
import { employeeCarouselImages } from "@/shared/constants";

const mockEmployee = {
  name: "<PERSON>",
  designation: "AI Researcher",
  description: "Expert in prompt engineering and large language models.",
  image: "/avatars/bob.png",
  carouselImages: employeeCarouselImages,
};

describe("EmployeeCard", () => {
  it("renders name, designation, and description", () => {
    render(<EmployeeCard {...mockEmployee} />);

    // Using regex to avoid double match issue
    expect(screen.getByText(/Bob.*AI Researcher/i)).toBeInTheDocument();
    expect(
      screen.getByText(mockEmployee.description, { exact: false })
    ).toBeInTheDocument();
  });

  it("renders View and Add Employee buttons", () => {
    render(<EmployeeCard {...mockEmployee} />);

    expect(screen.getByRole("button", { name: /view/i })).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: /add employee/i })
    ).toBeInTheDocument();
  });

  it("opens dialog when View button is clicked", () => {
    render(<EmployeeCard {...mockEmployee} />);
    const viewBtn = screen.getByRole("button", { name: /view/i });
    fireEvent.click(viewBtn);

    expect(screen.getAllByText(/Bob.*AI Researcher/i)[1]).toBeInTheDocument(); // Second instance should be from dialog
    expect(screen.getByText(/how to use this employee/i)).toBeInTheDocument();
  });

  it("shows Plus icon in Add Employee button", () => {
    render(<EmployeeCard {...mockEmployee} />);
    const addBtn = screen.getByRole("button", { name: /add employee/i });

    const svg = addBtn.querySelector("svg");
    expect(svg).toBeInTheDocument();
  });
});
