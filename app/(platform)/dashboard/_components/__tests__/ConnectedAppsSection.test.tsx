import { render, screen } from "@testing-library/react";
import { ConnectedAppsSection } from "../ConnectedAppsSection";

jest.mock("@/shared/constants", () => ({
  connectedApps: [],
}));

describe("ConnectedAppsSection", () => {
  it("renders heading and CirclePlus icon", () => {
    render(<ConnectedAppsSection />);
    expect(screen.getByText("Connected Apps")).toBeInTheDocument();

    // Use a safer check for presence of an SVG icon
    const svgElements = document.querySelectorAll("svg");
    expect(svgElements.length).toBeGreaterThan(0);
  });

  it("renders empty state message when no connected apps", () => {
    render(<ConnectedAppsSection />);
    expect(
      screen.getByText(/Connect To External Communication Apps Here/i)
    ).toBeInTheDocument();
  });
});
