import { render, screen, fireEvent } from "@testing-library/react";
import { NotificationSection } from "../NotificationSection";

// Mock the useNotificationStore hook
jest.mock("@/hooks/use-notification", () => ({
  useNotificationStore: () => ({
    closeNotification: jest.fn(),
  }),
}));

// Mock the Button component
jest.mock("@/components/ui/button", () => ({
  Button: ({ children, onClick }: any) => (
    <button onClick={onClick} data-testid="back-button">
      {children}
    </button>
  ),
}));

// Mock the Separator component
jest.mock("@/components/ui/separator", () => ({
  Separator: () => <hr data-testid="separator" />,
}));

// Mock the Tabs components
jest.mock("@/components/ui/tabs", () => ({
  Tabs: ({ children, defaultValue }: any) => (
    <div data-testid="tabs">{children}</div>
  ),
  TabsList: ({ children }: any) => (
    <div data-testid="tabs-list">{children}</div>
  ),
  TabsTrigger: ({ children, value }: any) => (
    <button data-testid={`tab-${value}`}>{children}</button>
  ),
  TabsContent: ({ children, value }: any) => (
    <div data-testid={`tab-content-${value}`}>{children}</div>
  ),
}));

describe("NotificationSection", () => {
  it("renders the notification section with header and notifications", () => {
    render(<NotificationSection />);

    // Check if the header is rendered
    expect(screen.getByText("Notifications")).toBeInTheDocument();

    // Check if the back button is rendered
    expect(screen.getByTestId("back-button")).toBeInTheDocument();

    // Check if tabs are rendered
    expect(screen.getByTestId("tabs")).toBeInTheDocument();
    expect(screen.getByTestId("tabs-list")).toBeInTheDocument();
    expect(screen.getByTestId("tab-all")).toBeInTheDocument();
    expect(screen.getByTestId("tab-unread")).toBeInTheDocument();
    expect(screen.getByText("All")).toBeInTheDocument();
    expect(screen.getByText("Unread")).toBeInTheDocument();

    // Check if tab contents are rendered
    expect(screen.getByTestId("tab-content-all")).toBeInTheDocument();
    expect(screen.getByTestId("tab-content-unread")).toBeInTheDocument();

    // Check if notifications are rendered
    expect(
      screen.getByText("New message from HR Assistant")
    ).toBeInTheDocument();
    expect(screen.getByText("System update")).toBeInTheDocument();
    expect(screen.getByText("New employee added")).toBeInTheDocument();

    // Check if time stamps are rendered
    expect(screen.getByText("2 hours ago")).toBeInTheDocument();
    expect(screen.getByText("Yesterday")).toBeInTheDocument();
    expect(screen.getByText("3 days ago")).toBeInTheDocument();

    // Check if separators are rendered
    const separators = screen.getAllByTestId("separator");
    expect(separators.length).toBeGreaterThanOrEqual(3); // At least one for each notification
  });

  it("calls closeNotification when back button is clicked", () => {
    const { useNotificationStore } = require("@/hooks/use-notification");
    const closeMock = jest.fn();

    // Override the mock implementation for this test
    useNotificationStore.mockReturnValue({
      closeNotification: closeMock,
    });

    render(<NotificationSection />);

    // Click the back button
    const backButton = screen.getByTestId("back-button");
    fireEvent.click(backButton);

    // Check if closeNotification was called
    expect(closeMock).toHaveBeenCalledTimes(1);
  });

  it("applies different styles to read and unread notifications", () => {
    render(<NotificationSection />);

    // Get all notification elements
    const notifications = screen.getAllByText(/New|System/);

    // Check if at least one notification has the unread style (bg-brand-clicked)
    const unreadNotification = document.querySelector(".bg-brand-clicked");
    expect(unreadNotification).toBeInTheDocument();

    // Check if at least one notification has the read style (bg-brand-card)
    const readNotification = document.querySelector(".bg-brand-card");
    expect(readNotification).toBeInTheDocument();
  });

  it("shows the highlighted 'notification' text in the empty state", () => {
    // Temporarily mock MockNotifications to be empty
    jest.mock("@/shared/constants", () => ({
      MockNotifications: [],
    }));

    render(<NotificationSection />);

    // Check if the empty state message is rendered with highlighted text
    const highlightedText = screen.getAllByText("notification");
    expect(highlightedText.length).toBeGreaterThanOrEqual(1);
  });
});
