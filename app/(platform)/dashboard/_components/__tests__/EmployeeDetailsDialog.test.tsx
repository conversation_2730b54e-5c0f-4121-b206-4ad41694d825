import { render, screen, fireEvent } from "@testing-library/react";
import EmployeeDialog from "../EmployeeDetailsDialog";

// Mock data
const mockEmployee = {
  name: "<PERSON>",
  designation: "AI Assistant",
  image: "/avatars/alice.png",
  description: "Helps manage your dashboard and answer queries.",
  carouselImages: ["/img1.png", "/img2.png"],
};

describe("EmployeeDialog", () => {
  it("opens dialog and displays employee info", () => {
    render(<EmployeeDialog employee={mockEmployee} />);

    // Check initial button
    const viewButton = screen.getByRole("button", { name: /view/i });
    expect(viewButton).toBeInTheDocument();

    // Click to open dialog
    fireEvent.click(viewButton);

    // Check employee name + designation
    expect(screen.getByText(/Alice the AI Assistant/i)).toBeInTheDocument();

    // Check description
    expect(
      screen.getByText(/Helps manage your dashboard and answer queries/i)
    ).toBeInTheDocument();

    // Check "Add Employee" button
    expect(
      screen.getByRole("button", { name: /add employee/i })
    ).toBeInTheDocument();

    // Check how-to-use text
    expect(screen.getByText(/how to use this employee/i)).toBeInTheDocument();
  });
});
