import { render, screen, fireEvent } from "@testing-library/react";
import { DashboardSidebar } from "../DashboardSidebar";

// Mock the useNotificationStore hook
jest.mock("@/hooks/use-notification", () => ({
  useNotificationStore: () => ({
    isNotificationOpen: false,
    closeNotification: jest.fn(),
  }),
}));

jest.mock("../LeftBar", () => ({
  LeftBar: ({ isSecondaryBarOpen, toggleSecondaryBar }: any) => (
    <div>
      <p>LeftBar</p>
      <button onClick={toggleSecondaryBar}>
        {isSecondaryBarOpen ? "Close Secondary" : "Open Secondary"}
      </button>
    </div>
  ),
}));

jest.mock("../EmployeeSection", () => ({
  EmployeeSection: () => <p>EmployeeSection</p>,
}));

jest.mock("../ConnectedAppsSection", () => ({
  ConnectedAppsSection: () => <p>ConnectedAppsSection</p>,
}));

jest.mock("../NotificationSection", () => ({
  NotificationSection: () => <p>NotificationSection</p>,
}));

jest.mock("../CreditsBox", () => ({
  CreditsBox: () => <p>CreditsBox</p>,
}));

describe("DashboardSidebar", () => {
  it("renders LeftBar and secondary sidebar content with employee section by default", () => {
    render(<DashboardSidebar />);

    expect(screen.getByText("LeftBar")).toBeInTheDocument();
    expect(screen.getByText("RUH AI")).toBeInTheDocument();
    expect(screen.getByText("EmployeeSection")).toBeInTheDocument();
    expect(screen.getByText("ConnectedAppsSection")).toBeInTheDocument();
    expect(screen.getByText("CreditsBox")).toBeInTheDocument();
    expect(screen.queryByText("NotificationSection")).not.toBeInTheDocument();
  });

  it("renders notification section when notifications are open", () => {
    // Override the mock implementation for this test
    const { useNotificationStore } = require("@/hooks/use-notification");
    useNotificationStore.mockReturnValue({
      isNotificationOpen: true,
      closeNotification: jest.fn(),
    });

    render(<DashboardSidebar />);

    expect(screen.getByText("LeftBar")).toBeInTheDocument();
    expect(screen.getByText("RUH AI")).toBeInTheDocument();
    expect(screen.getByText("NotificationSection")).toBeInTheDocument();
    expect(screen.queryByText("EmployeeSection")).not.toBeInTheDocument();
    expect(screen.queryByText("ConnectedAppsSection")).not.toBeInTheDocument();
    expect(screen.getByText("CreditsBox")).toBeInTheDocument();
  });

  it("toggles secondary sidebar visibility", () => {
    const { container } = render(<DashboardSidebar />);

    const sidebar = container.querySelector("div.flex.h-screen");

    // Initially should be visible (has padding and width classes)
    expect(sidebar).toHaveClass("w-[280px]");
    expect(sidebar).toHaveClass("opacity-100");

    // Find the toggle icon (PanelLeftCloseIcon) and click it
    const toggleIcon = container.querySelector("svg.cursor-pointer");
    expect(toggleIcon).toBeInTheDocument();

    toggleIcon && fireEvent.click(toggleIcon);

    // After toggle, sidebar should have hidden classes
    expect(sidebar).toHaveClass("w-0");
    expect(sidebar).toHaveClass("opacity-0");
  });
});
