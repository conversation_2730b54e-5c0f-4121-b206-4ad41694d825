import { render, screen, fireEvent } from "@testing-library/react";
import { LeftBar } from "../LeftBar";

// ✅ Fix: mock usePathname to return string (prevents .includes crash)
jest.mock("next/navigation", () => ({
  usePathname: jest.fn(() => "/dashboard"),
}));

// ✅ Mock constants
jest.mock("@/shared/constants", () => ({
  noTextLogoPath: "/logo.svg",
  leftBarItems: [
    {
      name: "Home",
      href: "/dashboard",
      icon: () => <svg data-testid="icon-home" />,
    },
    {
      name: "Profile",
      href: "/profile",
      icon: () => <svg data-testid="icon-profile" />,
    },
  ],
}));

// ✅ Mock routes
jest.mock("@/shared/routes", () => ({
  dashboardRoute: "/dashboard",
}));

// ✅ Mock Image component from Next.js
jest.mock("next/image", () => (props: any) => {
  return <img {...props} alt={props.alt || "mock-img"} />;
});

// ✅ Mock ModeToggle, Separator, UserAvatar, and NotificationButton
jest.mock("@/components/shared/ModeToggle", () => ({
  ModeToggle: () => <div data-testid="mode-toggle">Toggle</div>,
}));
jest.mock("@/components/shared/CustomSeparator", () => ({
  CustomSeparator: () => <div data-testid="separator" />,
}));
jest.mock("@/components/shared/UserAvatar", () => ({
  UserAvatar: ({ className }: { className?: string }) => (
    <div data-testid="user-avatar" className={className}>
      User
    </div>
  ),
}));
jest.mock("@/components/shared/NotificationButton", () => ({
  NotificationButton: () => (
    <div data-testid="notification-button">Notifications</div>
  ),
}));

// ✅ Mock useUserStore
jest.mock("@/hooks/use-user", () => ({
  useUserStore: () => ({
    user: {
      fullName: "John Doe",
      email: "<EMAIL>",
    },
  }),
}));

describe("LeftBar", () => {
  it("renders all UI sections correctly", () => {
    const toggleFn = jest.fn();
    render(
      <LeftBar isSecondaryBarOpen={false} toggleSecondaryBar={toggleFn} />
    );

    // Logo
    expect(screen.getByAltText("Logo")).toBeInTheDocument();

    // Nav items
    expect(screen.getByText("Home")).toBeInTheDocument();
    expect(screen.getByText("Profile")).toBeInTheDocument();
    expect(screen.getByTestId("icon-home")).toBeInTheDocument();
    expect(screen.getByTestId("icon-profile")).toBeInTheDocument();

    // Separators
    expect(screen.getAllByTestId("separator").length).toBeGreaterThanOrEqual(1);

    // Theme toggle
    expect(screen.getByTestId("mode-toggle")).toBeInTheDocument();

    // Notification button
    expect(screen.getByTestId("notification-button")).toBeInTheDocument();

    // User avatar
    expect(screen.getByTestId("user-avatar")).toBeInTheDocument();
  });

  it("calls toggleSecondaryBar when toggle button is clicked", () => {
    const toggleFn = jest.fn();
    render(
      <LeftBar isSecondaryBarOpen={false} toggleSecondaryBar={toggleFn} />
    );

    const toggleBtn =
      screen.getByRole("button", { hidden: true }) ||
      screen.getByTestId("toggle-btn");
    fireEvent.click(toggleBtn);
    expect(toggleFn).toHaveBeenCalled();
  });

  it("does NOT show toggle button when secondary bar is open", () => {
    const toggleFn = jest.fn();
    render(<LeftBar isSecondaryBarOpen={true} toggleSecondaryBar={toggleFn} />);

    expect(screen.queryByTestId("toggle-btn")).not.toBeInTheDocument();
  });
});
