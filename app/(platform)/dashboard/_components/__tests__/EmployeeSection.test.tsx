import { render, screen, fireEvent } from "@testing-library/react";
import { EmployeeSection } from "../EmployeeSection";

//  Mock employee data
jest.mock("@/shared/constants", () => ({
  employees: [
    {
      id: "emp1",
      name: "<PERSON>",
      avatar: "/avatars/alice.png",
    },
    {
      id: "emp2",
      name: "<PERSON>",
      avatar: "/avatars/bob.png",
    },
  ],
}));

//  Mock pathname
jest.mock("next/navigation", () => ({
  usePathname: () => "/dashboard/employee/emp1",
}));

describe("EmployeeSection", () => {
  it("renders the section header and icon", () => {
    render(<EmployeeSection />);
    expect(screen.getByText(/your employees/i)).toBeInTheDocument();
    expect(
      screen.getByRole("heading", { name: /your employees/i })
    ).toBeInTheDocument();
  });

  it("renders the tab triggers", () => {
    render(<EmployeeSection />);
    expect(screen.getByRole("tab", { name: /cards/i })).toBeInTheDocument();
    expect(screen.getByRole("tab", { name: /list/i })).toBeInTheDocument();
  });

  it("renders employee cards correctly", () => {
    render(<EmployeeSection />);
    expect(screen.getByText("Alice")).toBeInTheDocument();
    expect(screen.getByText("Bob")).toBeInTheDocument();
    expect(screen.getAllByRole("img").length).toBeGreaterThanOrEqual(2);
  });

  it("switches to list view when clicking list tab", () => {
    render(<EmployeeSection />);
    const listTab = screen.getByRole("tab", { name: /list/i });
    fireEvent.click(listTab);
    expect(screen.getByText("Alice")).toBeInTheDocument();
    expect(screen.getByText("Bob")).toBeInTheDocument();
  });
});
