import { render, screen } from "@testing-library/react";
import { AddEmployeeSection } from "../AddEmployeeSection";

describe("AddEmployeeSection", () => {
  it("renders image, text, and button", () => {
    render(<AddEmployeeSection />);

    // Check if the image renders with the correct alt text
    const image = screen.getByAltText("Add Employee");
    expect(image).toBeInTheDocument();

    // Check for the instructional text
    expect(
      screen.getByText(
        /click below button to create an ai employee from scratch/i
      )
    ).toBeInTheDocument();

    // Check if the button is rendered
    expect(
      screen.getByRole("button", { name: /create employee/i })
    ).toBeInTheDocument();

    // Optional: check if the SVG icon exists (safely)
    const svgIcons = document.querySelectorAll("svg");
    expect(svgIcons.length).toBeGreaterThan(0);
  });
});
