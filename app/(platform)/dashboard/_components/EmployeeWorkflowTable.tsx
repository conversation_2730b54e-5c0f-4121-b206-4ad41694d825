import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Minus, ChevronRight, Loader2, Package2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import React, { useState, useEffect } from "react";
import { sanitizeString, generatePaginationItems } from "@/services/helper";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { WorkflowInDB, PaginationMetadata } from "@/shared/interfaces";
import { workflowApi } from "@/app/api/workflow";
import { MCPCategory } from "@/shared/enums";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface Workflow extends WorkflowInDB {
  isAdded: boolean;
}

export const EmployeeWorkflowTable = ({
  setWorkflows,
  initialWorkflowIds,
}: {
  setWorkflows: (workflows: string[]) => void;
  initialWorkflowIds: string[];
}) => {
  const [activeCategory, setActiveCategory] = useState<string>("All");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [addedWorkflowIds, setAddedWorkflowIds] = useState<Set<string>>(
    new Set(initialWorkflowIds)
  );
  const [metadata, setMetadata] = useState<PaginationMetadata | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [pageLimit, setPageLimit] = useState<number>(10);

  const {
    data: workflowsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["workflows", pageNumber, pageLimit],
    queryFn: async () => {
      const response = await workflowApi.getWorkflowsByUser(
        pageNumber,
        pageLimit
      );
      setMetadata(response?.metadata);
      return response;
    },
  });

  useEffect(() => {
    setWorkflows(Array.from(addedWorkflowIds));
  }, [addedWorkflowIds, setWorkflows]);

  useEffect(() => {
    const newInitialSet = new Set(initialWorkflowIds);
    if (
      newInitialSet.size !== addedWorkflowIds.size ||
      !initialWorkflowIds.every((id) => addedWorkflowIds.has(id))
    ) {
      setAddedWorkflowIds(newInitialSet);
    }
  }, [initialWorkflowIds]);

  const handleAddWorkflow = (workflowId: string) => {
    setAddedWorkflowIds((prevIds) => {
      const newIds = new Set(prevIds).add(workflowId);
      return newIds;
    });
  };

  const handleRemoveWorkflow = (workflowId: string) => {
    setAddedWorkflowIds((prevIds) => {
      const newIds = new Set(prevIds);
      newIds.delete(workflowId);
      return newIds;
    });
  };

  const handleCategoryClick = (categoryKey: string) => {
    setActiveCategory(categoryKey);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handlePageChange = (page: number) => {
    setPageNumber(page);
  };

  const workflows: Workflow[] = React.useMemo(() => {
    if (!workflowsData?.data) return [];
    // Handle both nested and direct array response
    const workflowArray = Array.isArray(workflowsData.data)
      ? workflowsData.data
      : workflowsData.data || [];
    return workflowArray.map((workflow) => ({
      ...workflow,
      isAdded: addedWorkflowIds.has(workflow.id),
    }));
  }, [workflowsData, addedWorkflowIds]);

  const filteredWorkflows = workflows.filter((workflow) => {
    const categoryMatch =
      activeCategory === "All" || workflow.category === activeCategory;
    const searchMatch = workflow.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    return categoryMatch && searchMatch;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-brand-primary" />
      </div>
    );
  }

  if (error || !workflowsData) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-brand-secondary-font">
        <Package2 className="h-12 w-12 mb-4" />
        <p>Error fetching workflows</p>
      </div>
    );
  }

  // Check for empty data in both nested and direct array response
  const workflowArray = Array.isArray(workflowsData.data)
    ? workflowsData.data
    : workflowsData.data || [];
  if (workflowArray.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-brand-secondary-font">
        <Package2 className="h-12 w-12 mb-4" />
        <p>No data found</p>
      </div>
    );
  }

  const truncateDescription = (description: string | null) => {
    if (!description) return "No description";
    return description.length > 50
      ? `${description.slice(0, 50)}...`
      : description;
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <div></div>
        <Input
          placeholder="Search workflows by name..."
          className="w-full max-w-xs sm:max-w-sm md:max-w-md"
          value={searchTerm}
          onChange={handleSearchChange}
        />
      </div>
      <CategoryRow
        activeCategory={activeCategory}
        onCategoryClick={handleCategoryClick}
      />
      <div className="rounded-lg border border-brand-stroke bg-brand-card shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="border-b-brand-stroke">
              <TableHead className="w-[200px] px-6 py-4 text-brand-primary-font font-semibold">
                Workflows
              </TableHead>
              <TableHead className="px-6 py-4 text-brand-primary-font font-semibold">
                Description
              </TableHead>
              <TableHead className="px-6 py-4 text-brand-primary-font font-semibold">
                Category
              </TableHead>
              <TableHead className="text-right px-6 py-4 text-brand-primary-font font-semibold">
                Add workflow
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredWorkflows.map((workflow) => {
              const isWorkflowAdded = addedWorkflowIds.has(workflow.id);
              return (
                <TableRow
                  key={workflow.id}
                  className={`border-b-brand-stroke ${
                    isWorkflowAdded ? "bg-brand-card-hover" : ""
                  }`}
                >
                  <TableCell className="px-6 py-4">
                    <div className="flex items-center space-x-3">
                      <span className="font-medium text-brand-primary-font">
                        {workflow.name}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-4 text-brand-secondary-font">
                    {truncateDescription(workflow.description)}
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <Badge
                      variant="outline"
                      className="border-brand-stroke bg-brand-background text-brand-secondary-font"
                    >
                      {sanitizeString(workflow.category)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right px-6 py-4">
                    {isWorkflowAdded ? (
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-brand-primary text-brand-white-text hover:bg-brand-primary/90 border-brand-primary"
                        onClick={() => handleRemoveWorkflow(workflow.id)}
                      >
                        <Minus className="mr-2 h-4 w-4" />
                        Remove
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-brand-primary text-brand-primary hover:bg-brand-clicked"
                        onClick={() => handleAddWorkflow(workflow.id)}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
      {metadata && metadata.totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (metadata?.hasPreviousPage) {
                    handlePageChange(pageNumber - 1);
                  }
                }}
                className={
                  !metadata?.hasPreviousPage
                    ? "pointer-events-none opacity-50"
                    : "cursor-pointer"
                }
              />
            </PaginationItem>

            {generatePaginationItems(metadata).map((item, index) => (
              <PaginationItem key={index}>
                {typeof item === "string" ? (
                  <PaginationEllipsis />
                ) : (
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      handlePageChange(item);
                    }}
                    isActive={item === metadata.currentPage}
                    className="cursor-pointer"
                  >
                    {item}
                  </PaginationLink>
                )}
              </PaginationItem>
            ))}

            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (metadata?.hasNextPage) {
                    handlePageChange(pageNumber + 1);
                  }
                }}
                className={
                  !metadata?.hasNextPage
                    ? "pointer-events-none opacity-50"
                    : "cursor-pointer"
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
};

interface CategoryRowProps {
  activeCategory: string;
  onCategoryClick: (categoryKey: string) => void;
}

const CategoryRow = ({ activeCategory, onCategoryClick }: CategoryRowProps) => {
  const displayCategories = ["All", ...Object.values(MCPCategory)];

  return (
    <div className="flex items-center w-full py-1">
      <div className="flex-grow overflow-x-auto whitespace-nowrap space-x-2 no-scrollbar">
        {displayCategories.map((category) => {
          const isActive = activeCategory === category;
          const isAllButton = category === "All";
          return (
            <Button
              key={category as string}
              variant="outline"
              className={cn(
                "rounded-lg h-9 align-middle text-sm flex-shrink-0",
                "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                isAllButton ? "px-5" : "px-3",
                isActive && isAllButton
                  ? "bg-brand-primary text-brand-white-text hover:bg-brand-primary/90 border-transparent"
                  : isActive
                  ? "bg-brand-primary/10 text-brand-primary border-brand-primary hover:bg-brand-primary/20"
                  : "bg-brand-input text-brand-secondary-font hover:bg-brand-clicked border-brand-stroke dark:bg-brand-card dark:text-brand-secondary-font dark:hover:bg-brand-primary/10 dark:border-brand-stroke"
              )}
              onClick={() => onCategoryClick(category as string)}
            >
              {sanitizeString(category as string)}
            </Button>
          );
        })}
      </div>
      <Button
        variant="ghost"
        size="icon"
        className="flex-shrink-0 text-brand-secondary-font hover:text-brand-primary-font dark:text-brand-secondary-font dark:hover:text-brand-white-text ml-1"
      >
        <ChevronRight className="h-5 w-5" />
      </Button>
    </div>
  );
};
