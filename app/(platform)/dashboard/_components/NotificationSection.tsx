"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { useNotificationStore } from "@/hooks/use-notification";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { MockNotifications } from "@/shared/constants";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export function NotificationSection() {
  const { closeNotification } = useNotificationStore();

  // Filter notifications for unread tab
  const unreadNotifications = MockNotifications.filter(
    (notification) => !notification.read
  );

  return (
    <div className="flex flex-col w-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={closeNotification}
            className="h-8 w-8"
            data-testid="back-button"
          >
            <ArrowLeft size={18} strokeWidth={1.5} />
          </Button>
          <h2 className="text-brand-primary-font font-semibold text-sm">
            Notifications
          </h2>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="w-full justify-start gap-4 bg-transparent border-b border-brand-stroke rounded-none">
          <TabsTrigger
            value="all"
            className="text-sm font-medium text-brand-secondary-font data-[state=active]:text-brand-primary data-[state=active]:border-b-2 data-[state=active]:border-brand-primary data-[state=active]:bg-transparent rounded-none"
          >
            All
          </TabsTrigger>
          <TabsTrigger
            value="unread"
            className="text-sm font-medium text-brand-secondary-font data-[state=active]:text-brand-primary data-[state=active]:border-b-2 data-[state=active]:border-brand-primary data-[state=active]:bg-transparent rounded-none"
          >
            Unread
          </TabsTrigger>
        </TabsList>

        {/* All Notifications Tab */}
        <TabsContent value="all" className="pt-4">
          <div className="flex flex-col gap-2">
            {MockNotifications.length === 0 ? (
              <div className="flex flex-col items-center justify-center">
                <div className="relative mb-4">
                  <BellIcon
                    size={30}
                    strokeWidth={1.2}
                    className="text-brand-secondary-font"
                  />
                </div>
                <p className="text-brand-secondary-font text-sm">
                  No notification yet
                </p>
              </div>
            ) : (
              MockNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 rounded-md cursor-pointer transition-colors ${
                    notification.read
                      ? "bg-brand-card hover:bg-brand-card-hover"
                      : "bg-brand-clicked hover:bg-brand-clicked/90"
                  }`}
                >
                  <div className="flex flex-col gap-1">
                    <h3 className="text-brand-primary-font font-medium text-sm">
                      {notification.title}
                    </h3>
                    <p className="text-brand-secondary-font text-xs">
                      {notification.description}
                    </p>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-brand-secondary-font text-xs">
                        {notification.time}
                      </span>
                      {!notification.read && (
                        <span className="w-2 h-2 rounded-full bg-brand-primary"></span>
                      )}
                    </div>
                  </div>
                  <Separator className="mt-2" data-testid="separator" />
                </div>
              ))
            )}
          </div>
        </TabsContent>

        {/* Unread Notifications Tab */}
        <TabsContent value="unread" className="pt-4">
          <div className="flex flex-col gap-2">
            {unreadNotifications.length === 0 ? (
              <div className="flex flex-col items-center justify-center">
                <div className="relative mb-4">
                  <BellIcon
                    size={30}
                    strokeWidth={1.2}
                    className="text-brand-secondary-font"
                  />
                </div>
                <p className="text-brand-secondary-font text-sm">
                  No unread notification yet
                </p>
              </div>
            ) : (
              unreadNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className="p-3 rounded-md cursor-pointer transition-colors bg-brand-clicked hover:bg-brand-clicked/90"
                >
                  <div className="flex flex-col gap-1">
                    <h3 className="text-brand-primary-font font-medium text-sm">
                      {notification.title}
                    </h3>
                    <p className="text-brand-secondary-font text-xs">
                      {notification.description}
                    </p>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-brand-secondary-font text-xs">
                        {notification.time}
                      </span>
                      <span className="w-2 h-2 rounded-full bg-brand-primary"></span>
                    </div>
                  </div>
                  <Separator className="mt-2" data-testid="separator" />
                </div>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
