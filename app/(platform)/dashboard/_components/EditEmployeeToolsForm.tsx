"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useState, useEffect } from "react";
import { EmployeeToolsTable } from "./EmployeeToolsTable";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { Agent, MCPInDB } from "@/shared/interfaces";
import { editEmployeeProfileSchema } from "@/lib/schemas/editEmployee";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";
import { ChevronLeftIcon, PlusIcon, WrenchIcon, Loader2 } from "lucide-react";
import { mcpApi } from "@/app/api/mcp";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { agentApi } from "@/app/api/agent";
import { toast } from "sonner";

const editEmployeeToolsSchema = editEmployeeProfileSchema.pick({
  mcp_server_ids: true,
});

type EditToolsSchema = z.infer<typeof editEmployeeToolsSchema>;

export const EditEmployeeToolsForm = ({ agent }: { agent: Agent }) => {
  const [tools, setTools] = useState<string[]>(agent.mcp_server_ids || []);
  const [openToolTable, setOpenToolTable] = useState(false);
  const { closeModal } = useEmployeeEditStore();
  const queryClient = useQueryClient();

  // Fetch MCP data for the agent's tools
  const { data: mcpData, isLoading } = useQuery({
    queryKey: ["mcps-by-ids", tools],
    queryFn: () => mcpApi.getMcpsByIds(tools),
    enabled: tools.length > 0,
  });

  const form = useForm<EditToolsSchema>({
    resolver: zodResolver(editEmployeeToolsSchema),
    defaultValues: {
      mcp_server_ids: agent.mcp_server_ids || [],
    },
  });

  const { mutate: updateAgentTools, isPending: isUpdating } = useMutation({
    mutationFn: (formData: EditToolsSchema) =>
      agentApi.updateAgentTools(agent.id, formData),
  });

  const onSubmit = (formData: EditToolsSchema) => {
    formData.mcp_server_ids = tools;

    updateAgentTools(formData, {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["agents"] });
        queryClient.invalidateQueries({ queryKey: ["agent", agent.id] });
        toast.success("Employee tools updated successfully");
        closeModal();
      },
      onError: (error) => {
        toast.error(
          `Failed to update employee tools: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      },
    });
  };

  const handleRemoveTool = (toolId: string) => {
    setTools((prev) => {
      if (prev.includes(toolId)) {
        return prev.filter((id) => id !== toolId);
      } else {
        return [...prev, toolId];
      }
    });
  };

  return (
    <div className="flex flex-col h-[calc(100vh-200px)]">
      <div className="flex-1 flex flex-col gap-2 h-full justify-between overflow-y-auto p-10">
        {openToolTable ? (
          <div>
            <button
              onClick={() => setOpenToolTable(false)}
              className="flex flex-row gap-2 items-center"
            >
              <ChevronLeftIcon className="w-4 h-4" />
              <p className="text-brand-primary-font text-sm font-medium">
                Add Tools
              </p>
            </button>
            <EmployeeToolsTable setTools={setTools} initialToolIds={tools} />
          </div>
        ) : (
          <div className="flex flex-col gap-6">
            <div className="flex flex-row gap-2 w-full justify-between">
              <div className="flex flex-col gap-1">
                <h1 className="text-brand-primary-font text-sm font-medium">
                  Employee Tools
                </h1>
                <p className="text-xs text-brand-secondary-font">
                  Plugins that can enhance an employee's output. You can add
                  more tools to your employee here.
                </p>
              </div>
              <SecondaryButton
                className="w-30"
                onClick={() => setOpenToolTable(true)}
              >
                <PlusIcon className="w-4 h-4" />
                Add Tools
              </SecondaryButton>
            </div>
            <div className="flex flex-row flex-wrap gap-4 w-full">
              {isLoading ? (
                <div className="flex items-center justify-center w-full py-8">
                  <Loader2 className="w-6 h-6 animate-spin text-brand-primary" />
                </div>
              ) : mcpData?.mcps && mcpData.mcps.length > 0 ? (
                mcpData.mcps.map((tool) => (
                  <ToolCard
                    key={tool.id}
                    tool={tool}
                    onRemove={() => handleRemoveTool(tool.id)}
                    isRemoved={!tools.includes(tool.id)}
                  />
                ))
              ) : (
                <p className="text-brand-secondary-font text-sm">
                  No tools added yet
                </p>
              )}
            </div>
          </div>
        )}

        <p className="text-brand-secondary text-sm underline text-right">
          Explore More on the Marketplace
        </p>
      </div>

      <div className="flex w-full justify-end py-4 px-6 border-t border-brand-input-color gap-2">
        <SecondaryButton className="w-fit px-4" onClick={closeModal}>
          Cancel
        </SecondaryButton>
        <PrimaryButton
          className="w-fit px-4"
          disabled={!form.formState.isValid}
          onClick={form.handleSubmit(onSubmit)}
          isLoading={isUpdating}
        >
          Save Changes
        </PrimaryButton>
      </div>
    </div>
  );
};

interface ToolCardProps {
  tool: MCPInDB;
  onRemove: () => void;
  isRemoved: boolean;
}

const ToolCard = ({ tool, onRemove, isRemoved }: ToolCardProps) => {
  return (
    <div
      className={cn(
        "bg-brand-card rounded-lg p-6 border border-brand-stroke transition-all duration-200 w-[400px] max-w-[400px]",
        isRemoved && "opacity-50"
      )}
    >
      <div className="flex items-start justify-between gap-4">
        <div className="flex gap-4 items-start">
          {tool.logo ? (
            <Image
              src={tool.logo}
              alt={tool.name}
              width={24}
              height={24}
              className="rounded"
            />
          ) : (
            <WrenchIcon className="w-6 h-6" />
          )}

          <div className="flex flex-col gap-4">
            <h3 className="text-brand-primary-font font-medium">{tool.name}</h3>
            <p className="text-sm text-brand-secondary-font">
              {tool.department}
            </p>
            <p className="text-xs text-brand-secondary-font max-w-[400px]">
              {tool.description}
            </p>
            {isRemoved ? (
              <SecondaryButton className="w-24" onClick={onRemove}>
                Add
              </SecondaryButton>
            ) : (
              <PrimaryButton className="w-24" onClick={onRemove}>
                Remove
              </PrimaryButton>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
