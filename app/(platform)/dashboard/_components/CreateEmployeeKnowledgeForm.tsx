"use client";

import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { createEmployeeProfileSchema } from "@/lib/schemas/createEmployee";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { FileUpload } from "./FileUpload";
import { CirclePlusIcon, DownloadIcon, EyeIcon, Trash2 } from "lucide-react";
import { useState } from "react";
import { KnowledgeTable } from "./KnowledgeTable";
import { Input } from "@/components/ui/input";
// import { gcsApi, uploadToGCS } from "@/app/api/gcs"; // No longer needed here

const createKnowledgeSchema = createEmployeeProfileSchema.pick({
  files: true,
  urls: true,
});

type CreateKnowledgeSchema = z.infer<typeof createKnowledgeSchema>;

// Helper to get filename from GCS URL
const getFileNameFromUrl = (url: string): string => {
  try {
    const decodedUrl = decodeURIComponent(url);
    const path = new URL(decodedUrl).pathname;
    return path.substring(path.lastIndexOf("/") + 1);
  } catch (e) {
    // Fallback for non-URL strings or malformed URLs
    const lastSlash = url.lastIndexOf("/");
    return lastSlash >= 0 ? url.substring(lastSlash + 1) : url;
  }
};

export const CreateEmployeeKnowledgeForm = () => {
  const { setData, closeModal, formStep, setFormStep, data } =
    useEmployeeCreateStore();

  // const [uploadedFiles, setUploadedFiles] = useState<File[]>([]); // Removed, form.files will hold GCS URLs
  const [urls, setUrls] = useState<string[]>([]); // For manually added URLs
  const [openFilesTable, setOpenFilesTable] = useState(false);
  const [openUrlsTable, setOpenUrlsTable] = useState(false);
  const [urlInput, setUrlInput] = useState("");
  const [urlError, setUrlError] = useState<string | null>(null);
  const [isSubmittingForm, setIsSubmittingForm] = useState(false); // Renamed from isUploading
  const [isFileUploading, setIsFileUploading] = useState(false); // Controlled by FileUpload component

  const form = useForm<CreateKnowledgeSchema>({
    resolver: zodResolver(createKnowledgeSchema),
    defaultValues: {
      files: data.files || [], // Ensure it's an array
      urls: data.urls || [], // Ensure it's an array
    },
  });

  const maxFilesDisplay = 3; // How many files/URLs to show before "X more"

  const validateStorageUrl = (url: string): boolean => {
    const urlSchema = z
      .string()
      .url("Invalid URL format")
      .refine(
        (value) =>
          value.includes("drive.google.com") ||
          value.includes("onedrive.live.com"),
        {
          message: "URL must be a Google Drive or OneDrive link",
        }
      );
    try {
      urlSchema.parse(url);
      return true;
    } catch (error) {
      return false;
    }
  };

  const onSubmit = async (formData: CreateKnowledgeSchema) => {
    console.log("Form data before submission:", formData);
    setIsSubmittingForm(true);
    try {
      // formData.files already contains GCS URLs from FileUpload
      // formData.urls contains manually added URLs
      console.log("Form data after uploads:", formData);
      setData({
        ...formData,
        files: formData.files || [], // Ensure files is an array
        urls: formData.urls || [], // Ensure urls is an array
      });
      setFormStep(formStep + 1);
    } catch (error) {
      console.error("Error during form submission:", error);
      alert(
        "Form submission failed. Please check console for details and try again."
      );
    } finally {
      setIsSubmittingForm(false);
    }
  };

  // For GCS uploaded files (stored in form.files)
  const handleDeleteGcsFile = (urlToDelete: string) => {
    const currentFiles = form.getValues("files") || [];
    form.setValue(
      "files",
      currentFiles.filter((url) => url !== urlToDelete),
      { shouldValidate: true, shouldDirty: true }
    );
  };

  const handlePreviewGcsFile = (url: string) => {
    window.open(url, "_blank");
  };

  const handleDownloadGcsFile = (url: string) => {
    window.open(url, "_blank"); // GCS URL should be directly accessible
  };

  // For manually added URLs (stored in form.urls and local 'urls' state for UI before form set)
  const deleteManualUrl = (urlToDelete: string) => {
    const currentFormUrls = form.getValues("urls") || [];
    form.setValue(
      "urls",
      currentFormUrls.filter((u) => u !== urlToDelete),
      { shouldValidate: true, shouldDirty: true }
    );
    // also update local state if it's used for display before table
    setUrls((prevUrls) => prevUrls.filter((u) => u !== urlToDelete));
  };

  const previewManualUrl = (url: string) => {
    window.open(url, "_blank");
  };

  const watchedGcsFiles = form.watch("files") || [];
  const watchedManualUrls = form.watch("urls") || [];

  if (openFilesTable) {
    return (
      <KnowledgeTable
        // files={watchedGcsFiles.map(getFileNameFromUrl)} // If KnowledgeTable expects names
        files={watchedGcsFiles.map((url) => getFileNameFromUrl(url))} // Pass URLs, assume KnowledgeTable handles display
        closeTable={() => setOpenFilesTable(false)}
        deleteFile={(fileUrl) => handleDeleteGcsFile(fileUrl as string)}
        previewFile={(fileUrl) => handlePreviewGcsFile(fileUrl as string)}
        downloadFile={(fileUrl) => handleDownloadGcsFile(fileUrl as string)}
      />
    );
  }

  if (openUrlsTable) {
    return (
      <KnowledgeTable
        files={watchedManualUrls} // These are already URLs
        closeTable={() => setOpenUrlsTable(false)}
        deleteFile={(url) => deleteManualUrl(url as string)}
        previewFile={(url) => previewManualUrl(url as string)}
      />
    );
  }

  return (
    <div className="flex flex-col justify-between h-full p-4 ">
      <div className="flex flex-col gap-10 p-10 overflow-y-auto max-h-[calc(95vh-300px)]">
        <div className="flex flex-col gap-2">
          <h1 className="font-medium text-brand-primary-font">
            Employee's Knowledge Base
          </h1>
          <p className="text-brand-secondary-font text-sm">
            Add/remove specific documentation for the employee's memory and
            knowledge here, in the form of files and URLs.
          </p>
        </div>
        <div className="flex flex-col gap-4">
          {" "}
          {/* Ensure gap consistency */}
          <div className="flex flex-col gap-4">
            <h1 className="font-medium text-brand-primary-font">Files</h1>
            <FileUpload
              onUploadSuccess={(gcsUrls) => {
                const currentFiles = form.getValues("files") || [];
                form.setValue("files", [...currentFiles, ...gcsUrls], {
                  shouldValidate: true,
                  shouldDirty: true,
                });
              }}
              gcsPathPrefix="employee_knowledge/" // Ensure this is the correct path
              onUploadingStateChange={setIsFileUploading}
              acceptedFileTypes=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.svg,.webp,.mp3,.m4a,.wav,.mp4,.wmv"
            />
            <div className="flex flex-row items-center gap-2 flex-wrap">
              {watchedGcsFiles
                .slice(0, maxFilesDisplay)
                .map((gcsUrl, index) => (
                  <UploadedGcsFileDisplay
                    key={index}
                    url={gcsUrl}
                    onDelete={handleDeleteGcsFile}
                    onPreview={handlePreviewGcsFile}
                    onDownload={handleDownloadGcsFile}
                  />
                ))}
              {watchedGcsFiles.length > maxFilesDisplay && (
                <button
                  className="cursor-pointer"
                  onClick={() => setOpenFilesTable(true)}
                >
                  <h1 className="text-brand-secondary">
                    + {watchedGcsFiles.length - maxFilesDisplay} more
                  </h1>
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <h1 className="font-medium text-brand-primary-font">URLs</h1>
          <form
            className="flex flex-row items-center gap-2"
            onSubmit={(e) => {
              e.preventDefault();
              if (validateStorageUrl(urlInput)) {
                const currentFormUrls = form.getValues("urls") || [];
                if (!currentFormUrls.includes(urlInput)) {
                  form.setValue("urls", [...currentFormUrls, urlInput], {
                    shouldValidate: true,
                    shouldDirty: true,
                  });
                  // setUrls(prev => [...prev, urlInput]); // Local state 'urls' might not be needed if always reading from form
                }
                setUrlInput("");
                setUrlError(null);
              } else {
                setUrlError(
                  "Invalid URL. Please enter a valid Google Drive or OneDrive link."
                );
              }
            }}
          >
            <Input
              placeholder="Enter Google Drive or OneDrive URL"
              className="w-full"
              value={urlInput}
              onChange={(e) => {
                setUrlInput(e.target.value);
                if (urlError) setUrlError(null);
              }}
            />
            <button type="submit">
              <CirclePlusIcon
                className="w-8 h-8 text-brand-primary-font"
                strokeWidth={1.2}
              />
            </button>
          </form>
          {urlError && <p className="text-sm text-red-500 mt-1">{urlError}</p>}
        </div>
        <div className="flex flex-row items-center gap-2 flex-wrap">
          {watchedManualUrls?.slice(0, maxFilesDisplay).map((url, index) => (
            <UploadedManualUrlDisplay
              key={index}
              url={url}
              onDelete={deleteManualUrl}
              onPreview={previewManualUrl}
            />
          ))}
          {watchedManualUrls.length > maxFilesDisplay && (
            <button
              className="cursor-pointer"
              onClick={() => setOpenUrlsTable(true)}
            >
              <h1 className="text-brand-secondary">
                + {watchedManualUrls.length - maxFilesDisplay} more
              </h1>
            </button>
          )}
        </div>
      </div>

      {/* Fixed Bottom Section */}
      <div className="flex w-full justify-between py-2 border-t border-brand-input-color flex-none">
        <SecondaryButton className="w-fit px-4" onClick={closeModal}>
          Cancel
        </SecondaryButton>
        <div className="flex gap-2">
          <SecondaryButton
            className="w-fit px-4"
            onClick={() => setFormStep(formStep - 1)}
          >
            Back
          </SecondaryButton>
          <PrimaryButton
            className="w-fit px-4"
            disabled={
              !form.formState.isValid || isSubmittingForm || isFileUploading
            }
            onClick={form.handleSubmit(onSubmit)}
            isLoading={isSubmittingForm}
          >
            {isSubmittingForm
              ? "Submitting..."
              : isFileUploading
              ? "Uploading Files..."
              : "Next"}
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};

// Renamed and updated for GCS URLs
const UploadedGcsFileDisplay = ({
  url,
  onDelete,
  onPreview,
  onDownload,
}: {
  url: string;
  onDelete: (url: string) => void;
  onPreview: (url: string) => void;
  onDownload: (url: string) => void;
}) => {
  const fileName = getFileNameFromUrl(url);
  return (
    <div className="flex border border-brand-input-color p-2 gap-10  bg-brand-background rounded-sm justify-between items-center group max-w-xs">
      <div className="flex flex-col gap-1 overflow-hidden">
        <p className="text-brand-primary-font truncate" title={fileName}>
          {fileName.length > 20 ? fileName.slice(0, 20) + "..." : fileName}
        </p>
        {/* Size removed */}
      </div>
      <div className="flex opacity-0 group-hover:opacity-100 transition-opacity duration-200 shrink-0">
        <button
          className="p-2 rounded-sm "
          onClick={() => onPreview(url)}
          title="Preview File"
        >
          <EyeIcon className="w-4 h-4 text-brand-secondary-font" />
        </button>
        <button
          className="p-2 rounded-sm "
          onClick={() => onDownload(url)}
          title="Download File"
        >
          <DownloadIcon className="w-4 h-4 text-brand-secondary-font" />
        </button>
        <button
          className="p-2 rounded-sm "
          onClick={() => onDelete(url)}
          title="Delete File"
        >
          <Trash2 className="w-4 h-4 text-brand-secondary-font" />
        </button>
      </div>
    </div>
  );
};

// Renamed for clarity, assuming similar structure for manually added URLs
const UploadedManualUrlDisplay = ({
  url,
  onDelete,
  onPreview,
}: {
  url: string;
  onDelete: (url: string) => void;
  onPreview: (url: string) => void;
}) => {
  // For manual URLs, display a truncated version or a derived name if needed
  const displayName = url.length > 30 ? url.slice(0, 27) + "..." : url;
  return (
    <div className="flex border border-brand-input-color p-2 gap-10  bg-brand-background rounded-sm justify-between items-center group max-w-xs">
      <div className="flex flex-col gap-1 overflow-hidden">
        <p className="text-brand-primary-font truncate" title={url}>
          {displayName}
        </p>
      </div>
      <div className="flex opacity-0 group-hover:opacity-100 transition-opacity duration-200 shrink-0">
        <button
          className="p-2 rounded-sm "
          onClick={() => onPreview(url)}
          title="Preview URL"
        >
          <EyeIcon className="w-4 h-4 text-brand-secondary-font" />
        </button>
        <button
          className="p-2 rounded-sm "
          onClick={() => onDelete(url)}
          title="Delete URL"
        >
          <Trash2 className="w-4 h-4 text-brand-secondary-font" />
        </button>
      </div>
    </div>
  );
};
