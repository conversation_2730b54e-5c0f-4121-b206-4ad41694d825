"use client";

import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { createEmployeeProfileSchema } from "@/lib/schemas/createEmployee";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useState } from "react";
import { EmployeeToolsTable } from "./EmployeeToolsTable";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { PrimaryButton } from "@/components/shared/PrimaryButton";

const createToolsSchema = createEmployeeProfileSchema.pick({
  mcp_server_ids: true,
});

type CreateToolsSchema = z.infer<typeof createToolsSchema>;

export const CreateEmployeeToolsForm = () => {
  const [tools, setTools] = useState<string[]>([]);
  const { setData, closeModal, formStep, setFormStep, data } =
    useEmployeeCreateStore();

  const form = useForm<CreateToolsSchema>({
    resolver: zodResolver(createToolsSchema),
    defaultValues: {
      mcp_server_ids: data.mcp_server_ids,
    },
  });

  const onSubmit = (formData: CreateToolsSchema) => {
    formData.mcp_server_ids = tools;
    console.log(formData);
    setData(formData);
    setFormStep(formStep + 1);
  };

  return (
    <div className="h-full flex flex-col justify-between">
      <div className="h-full flex flex-col justify-between p-10 overflow-y-auto max-h-[calc(95vh-300px)]">
        <EmployeeToolsTable
          setTools={setTools}
          initialToolIds={data.mcp_server_ids ?? []}
        />
        <p className="text-brand-secondary text-sm underline text-right">
          Explore More on the Marketplace
        </p>
      </div>

      {/* Fixed Bottom Section */}
      <div className="flex w-full justify-between py-2 border-t border-brand-input-color flex-none">
        <SecondaryButton className="w-fit px-4" onClick={closeModal}>
          Cancel
        </SecondaryButton>
        <div className="flex gap-2">
          <SecondaryButton
            className="w-fit px-4"
            onClick={() => setFormStep(formStep - 1)}
          >
            Back
          </SecondaryButton>
          <PrimaryButton
            className="w-fit px-4"
            disabled={!form.formState.isValid}
            onClick={form.handleSubmit(onSubmit)}
          >
            Next
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};
