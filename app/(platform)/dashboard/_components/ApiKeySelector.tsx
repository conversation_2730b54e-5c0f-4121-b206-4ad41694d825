"use client";

import { useState, useEffect } from "react";
import { ChevronDown, Copy, Plus, Search, Loader2, Trash2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { mcpApi } from "@/app/api/credentials";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

interface ApiKey {
  id: string;
  key_name: string;
  value: string;
}

const formSchema = z.object({
  key_name: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(20, "Name must be less than 20 characters"),
  value: z.string().min(1, "API Key is required"),
});

export default function ApiKeySelector({
  onSelect,
  selectedKey,
  disabled,
}: {
  onSelect: (keyId: string) => void;
  selectedKey?: string;
  disabled?: boolean;
}) {
  const [open, setOpen] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedKeyName, setSelectedKeyName] = useState<string>("");

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      key_name: "",
      value: "",
    },
  });

  const queryClient = useQueryClient();

  // Fetch API keys
  const { data: apiKeys = [], isLoading } = useQuery({
    queryKey: ["credentials"],
    queryFn: async () => {
      const response = await mcpApi.getCredentials();
      return response.credentials;
    },
  });

  // Create API key mutation
  const createMutation = useMutation({
    mutationFn: (data: z.infer<typeof formSchema>) =>
      mcpApi.createCredential(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["credentials"] });
      setDialogOpen(false);
      form.reset();
    },
  });

  // Delete API key mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => mcpApi.deleteCredential(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["credentials"] });
    },
  });

  // Find the selected key name when the component mounts or when apiKeys/selectedKey changes
  useEffect(() => {
    if (selectedKey && apiKeys.length > 0) {
      const key = apiKeys.find((key) => key.id === selectedKey);
      if (key) {
        setSelectedKeyName(key.key_name);
      }
    }
  }, [selectedKey, apiKeys]);

  // Filter keys based on search query
  const filteredKeys = apiKeys.filter(
    (key) =>
      !searchQuery.trim() ||
      key.key_name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  function onSubmit(values: z.infer<typeof formSchema>) {
    createMutation.mutate(values);
  }

  return (
    <div className="w-full max-w-md">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between h-full"
            disabled={disabled}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <span className="truncate">
                {selectedKeyName || "Select an API key"}
              </span>
            )}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0" align="start">
          <div className="flex items-center justify-between border-b px-3 py-2">
            <h4 className="font-medium">Select API Key</h4>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => {
                setDialogOpen(true);
              }}
            >
              <Plus className="h-4 w-4" />
              <span className="sr-only">Add new API key</span>
            </Button>
          </div>

          <div className="border-b px-3 py-2">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search API keys..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          <div className="max-h-[300px] overflow-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-6 w-6 animate-spin text-brand-primary" />
              </div>
            ) : filteredKeys.length > 0 ? (
              filteredKeys.map((apiKey) => (
                <div
                  key={apiKey.id}
                  className={cn(
                    "flex cursor-pointer items-center justify-between px-3 py-2.5 hover:opacity-80",
                    selectedKey === apiKey.id && "bg-brand-primary/50"
                  )}
                  onClick={() => {
                    onSelect(apiKey.id);
                    setSelectedKeyName(apiKey.key_name);
                    setOpen(false);
                  }}
                >
                  <div className="flex flex-col gap-0.5">
                    <span className="font-medium">{apiKey.key_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 text-muted-foreground hover:text-destructive"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteMutation.mutate(apiKey.id);
                      }}
                      disabled={deleteMutation.isPending}
                    >
                      {deleteMutation.isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              ))
            ) : (
              <div className="px-3 py-6 text-center text-sm text-muted-foreground">
                No API keys found matching "{searchQuery}"
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>

      <Dialog
        open={dialogOpen}
        onOpenChange={(open) => {
          setDialogOpen(open);
          if (!open) form.reset();
        }}
      >
        <DialogContent className="sm:max-w-[425px] bg-brand-card">
          <DialogHeader>
            <DialogTitle className="text-brand-primary-font">
              Add New Credential
            </DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="key_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Credential Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter credential name..."
                        className="bg-brand-card border-brand-stroke"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Key</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Enter your API key..."
                        className="bg-brand-card border-brand-stroke"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setDialogOpen(false);
                    form.reset();
                  }}
                  className="border-brand-stroke text-brand-primary-font"
                  disabled={createMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-brand-primary text-white hover:bg-brand-primary/90"
                  disabled={createMutation.isPending}
                >
                  {createMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
