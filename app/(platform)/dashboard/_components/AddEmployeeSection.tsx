import Image from "next/image";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { PlusIcon } from "lucide-react";

const addEmployeeImage = "/assets/dashboard/add-employee.svg";

export const AddEmployeeSection = () => {
  return (
    <div className="flex flex-col items-center justify-center gap-6 p-4 ">
      <Image
        src={addEmployeeImage}
        alt="Add Employee"
        width={100}
        height={100}
      />
      <p className="text-brand-primary-font font-base text-center max-w-[500px]">
        If you&apos;re feeling creative, click below button to create an AI
        employee from scratch.
      </p>
      <SecondaryButton className="w-[70%]">
        <PlusIcon className="w-4 h-4" />
        Create employee
      </SecondaryButton>
    </div>
  );
};
