import type { Metada<PERSON> } from "next";
import "./globals.css";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import { siteConfig } from "@/shared/site.config";
import { noTextLogoPath } from "@/shared/constants";
import { SessionInitializer } from "@/components/auth/SessionInitializer";

// Primary Font
const sora = Sora({
  subsets: ["latin"],
  variable: "--font-sora",
});

// Secondary Font
const jost = Jost({
  subsets: ["latin"],
  variable: "--font-jost",
});

// Metadata for the website
export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`, // will adapt with the site name
  },
  description: siteConfig.description,
  // favicon
  icons: [
    {
      href: noTextLogoPath,
      url: noTextLogoPath,
    },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      suppressHydrationWarning
      className={`${jost.variable} ${sora.variable} antialiased `}
    >
      <body className="font-secondary bg-brand-background relative">
        <SessionInitializer>{children}</SessionInitializer>
      </body>
    </html>
  );
}
