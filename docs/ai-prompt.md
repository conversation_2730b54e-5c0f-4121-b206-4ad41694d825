# AI Development Guidelines for Ruh App

## Project Overview

This is a Next.js 15 application with React 19, using a modern tech stack including:

- **UI Framework**: shadcn/ui with Tailwind CSS
- **State Management**: Zustand
- **Form Handling**: React Hook Form with Zod validation
- **API Integration**: Axios with React Query
- **Authentication**: Token-based with secure cookie storage

## Development Guidelines

When implementing features or making changes to this codebase, please adhere to the following guidelines:

### Project Structure

Always respect the existing project structure:

```
ruh-app/
├── app/           # Next.js app directory
│   ├── (auth)/    # Authentication routes and components
│   ├── (platform)/# Platform routes and components
│   └── api/       # API client functions
├── components/    # Reusable UI components
│   ├── shared/    # Custom shared components
│   └── ui/        # shadcn/ui components
├── docs/          # Documentation files
├── hooks/         # Custom React hooks, for state management
├── lib/           # Utility functions and helpers
│   ├── providers/ # React providers (Query, Theme)
│   └── schemas/   # Zod validation schemas
├── public/        # Static assets
├── services/      # API services
│   ├── axios.ts   # Axios instance with interceptors
│   └── authCookies.ts # Authentication cookie management
└── shared/        # Shared constants, interfaces, and site config
```

For more detailed information, refer to `@docs/project-structure.md`.

### Code Style and Patterns

1. **DO NOT create files that already exist** - Check thoroughly before creating new files
2. **Follow existing patterns** - Examine similar components or features before implementing new ones
3. **Use TypeScript properly** - Define interfaces in `shared/interfaces.ts` when they're used across multiple files
4. **Component naming**:
   - Use PascalCase for components (e.g., `PrimaryButton.tsx`)
   - Use camelCase for utilities and hooks (e.g., `useUserStore.ts`)
5. **Route-specific components** should be placed in `_components/` folders within their route directories

### API Integration

1. **Use the centralized axios instance** from `services/axios.ts`
2. **Implement API functions** in the appropriate API file in `app/api/`
3. **Use React Query** for data fetching and mutations
4. **Handle errors consistently** with toast notifications for user feedback

### Authentication

1. **Use the auth API functions** from `app/api/auth.ts`
2. **Token management** should use functions from `services/authCookies.ts`
3. **Respect the onboarding flow** - Users must complete profile before accessing the dashboard

### State Management

1. **Use Zustand stores** for global state management
2. **Create focused stores** for specific features rather than one large store
3. **Persist necessary state** using Zustand middleware

### Component Development

1. **Make components reusable** when appropriate
2. **Use shadcn/ui components** as base building blocks
3. **Use existing components** like PrimaryButton, SecondaryButton instead of creating new ones
4. **Extend with custom styling** using Tailwind classes
5. **Implement proper loading states** for async operations

### Documentation and Comments

1. **Add meaningful comments for complex logic** - Explain "why" not "what"
2. **Don't over-comment simple code** - Keep the codebase clean
3. **Document new features** in the appropriate docs files

### Testing

1. **Write tests for new components** in a `__tests__` directory
2. **Follow the existing test patterns** using Jest and React Testing Library
3. **Test both success and error cases** for comprehensive coverage

## Implementation Checklist

When implementing a new feature or fixing a bug, follow this checklist:

- [ ] Understand the requirements thoroughly
- [ ] Check existing code for similar patterns
- [ ] Plan your implementation approach
- [ ] Create/modify files following the project structure
- [ ] Implement the feature with proper TypeScript typing
- [ ] Add appropriate error handling
- [ ] Test the implementation manually
- [ ] Write automated tests if applicable
- [ ] Remove any console.log statements
- [ ] Document complex logic with comments
- [ ] Update documentation if necessary

## Final Verification

Before considering a task complete, verify:

- [ ] Code follows the project structure and patterns
- [ ] No unnecessary files or code were created
- [ ] All features work as expected
- [ ] Error cases are handled properly
- [ ] Code is clean and well-documented where needed
- [ ] Tests pass (if applicable)
