import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { loginRoute } from "@/shared/routes";

// Auth routes that should be accessible without tokens
const publicRoutes = [loginRoute];

export function middleware(request: NextRequest) {
  // Get the pathname from the URL
  const pathname = request.nextUrl.pathname;

  // Check if the current route is a public route
  const isPublicRoute = publicRoutes.some((route) =>
    pathname.startsWith(route)
  );

  // Skip token check for public routes
  if (isPublicRoute) {
    return NextResponse.next();
  }

  // Check if the user has an auth token
  const hasAuthTokens =
    request.cookies.has("accessToken") || request.cookies.has("refreshToken");

  // If user is not logged in, redirect to login
  if (!hasAuthTokens) {
    const loginUrl = new URL(loginRoute, request.url);
    loginUrl.searchParams.set("reason", "no_tokens");
    return NextResponse.redirect(loginUrl);
  }

  return NextResponse.next();
}

// Configure middleware to run on all routes except api, _next/static, etc.
export const config = {
  matcher: ["/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"],
};
