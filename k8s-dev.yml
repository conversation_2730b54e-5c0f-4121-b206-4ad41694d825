apiVersion: v1
kind: ServiceAccount
metadata:
  name: ruh-user-app-fe-ai-sa
  namespace: ruh-dev
  labels:
    name: ruh-user-app-fe-ai-sa
    namespace: ruh-dev
    app: ruh-user-app-fe-ai
    deployment: ruh-user-app-fe-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ruh-user-app-fe-ai-dp
  namespace: ruh-dev
  labels:
    name: ruh-user-app-fe-ai-dp
    namespace: ruh-dev
    app: ruh-user-app-fe-ai
    serviceaccount: ruh-user-app-fe-ai-sa
    deployment: ruh-user-app-fe-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ruh-user-app-fe-ai
      deployment: ruh-user-app-fe-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-dev
        app: ruh-user-app-fe-ai
        deployment: ruh-user-app-fe-ai-dp
    spec:
      serviceAccountName: ruh-user-app-fe-ai-sa      
      containers:
      - name: ruh-user-app-fe-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        imagePullPolicy: IfNotPresent
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 3000
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: ruh-user-app-fe-ai-svc
  namespace: ruh-dev
spec:
  selector:
    app: ruh-user-app-fe-ai
    deployment: ruh-user-app-fe-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name: ruh-user-app-fe-user-hpa
#   namespace: ruh-dev
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name: ruh-user-app-fe-user-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ruh-user-app-fe-user-ingress
  namespace: ruh-dev
spec:
  ingressClassName: nginx
  rules:
  - host: ruh.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ruh-user-app-fe-ai-svc
            port:
              number: 80