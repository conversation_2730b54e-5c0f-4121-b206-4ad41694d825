"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { usePricingModalStore } from "@/hooks/use-pricing";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";
import { pricingPlans } from "@/shared/constants";
import { PrimaryButton } from "../shared/PrimaryButton";

export const PricingModal = () => {
  const { isOpen, closeModal } = usePricingModalStore();

  return (
    <Dialog open={isOpen} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[1100px] bg-brand-card p-8">
        <DialogHeader className="mb-8">
          <DialogTitle className="text-2xl text-brand-primary-font font-semibold text-center">
            Purchase a subscription
          </DialogTitle>
          <DialogDescription className="text-base text-brand-secondary-font text-center mt-2 ">
            Choose the plan that works for you.
          </DialogDescription>
        </DialogHeader>

        <div>
          <div className="grid grid-cols-3 gap-8">
            {pricingPlans.map((plan) => (
              <PricingCard key={plan.name} plan={plan} />
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const PricingCard = ({ plan }: { plan: any }) => {
  const isFreePlan = plan.name === "Free Plan";

  return (
    <div
      className={`flex flex-col px-6 py-8 rounded-xl border  min-h-[600px]  ${
        isFreePlan
          ? "bg-brand-tertiary text-brand-white-text border-brand-border-color/20"
          : "bg-brand-card border-brand-border-color"
      }`}
    >
      <div className="flex flex-col gap-8">
        <h3 className="text-lg font-semibold">{plan.name}</h3>
        <div className="flex gap-4">
          <p className="text-3xl font-bold font-primary ">{plan.price}</p>
          <p className="text-brand-secondary-font text-sm max-w-[130px] ">
            per editor/month billed monthly
          </p>
        </div>
      </div>
      <div className="flex-1 flex flex-col gap-4 mt-10 ">
        {plan.features.map((feature: string) => (
          <div key={feature} className="flex items-center gap-2">
            <div
              className={`${
                isFreePlan ? "bg-brand-white-text" : "bg-brand-background"
              } rounded-full p-1`}
            >
              <Check
                className="h-3 w-3 text-brand-secondary-font"
                strokeWidth={3}
              />
            </div>
            <p className="text-base font-light ">{feature}</p>
          </div>
        ))}
      </div>
      <PrimaryButton
        disabled={isFreePlan}
        className={`${
          isFreePlan
            ? "bg-brand-secondary-font text-brand-white-text"
            : "brand-gradient-indicator"
        } hover:opacity-70 transition-all duration-200`}
      >
        {isFreePlan ? "Current Plan" : "Choose Plan"}
      </PrimaryButton>
    </div>
  );
};
