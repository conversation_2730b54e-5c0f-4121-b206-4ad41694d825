"use client";

import { useEffect, useState, ReactNode, Suspense } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useUserStore } from "@/hooks/use-user";
import { initializeSessionFromCookie } from "@/app/api/auth";
import { loginRoute } from "@/shared/routes";
import { useOrgStore } from "@/hooks/use-organization";

interface SessionInitializerProps {
  children: ReactNode;
}

function SessionInitializerContent({ children }: SessionInitializerProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { isLoadingAuth, setIsLoadingAuth, clearUser } = useUserStore();
  const { isLoadingOrganization, setIsLoadingOrganization, clearOrganization } =
    useOrgStore();
  const [isInitialCheckComplete, setIsInitialCheckComplete] = useState(false);

  useEffect(() => {
    const performAuthCheck = async () => {
      // Check if we were redirected due to missing tokens
      const redirectReason = searchParams.get("reason");
      if (redirectReason === "no_tokens") {
        clearUser();
        clearOrganization();
        // Remove the reason parameter from URL
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete("reason");
        router.replace(newUrl.pathname);
        setIsLoadingAuth(false);
        setIsLoadingOrganization(false);
        setIsInitialCheckComplete(true);
        return;
      }

      // Get the current state once at the beginning to avoid inconsistent hook calls
      const { user: currentUserInStore, isLoadingAuth: currentLoadingState } =
        useUserStore.getState();

      if (currentUserInStore?.accessToken && !currentLoadingState) {
        setIsLoadingAuth(false);
        setIsLoadingOrganization(false);
        setIsInitialCheckComplete(true);
        return;
      }

      setIsLoadingAuth(true);
      setIsLoadingOrganization(true);
      try {
        const sessionInitialized = await initializeSessionFromCookie();

        if (sessionInitialized) {
          setIsLoadingAuth(false);
          setIsLoadingOrganization(false);
          setIsInitialCheckComplete(true);
        } else {
          clearUser();
          clearOrganization();
          setIsLoadingAuth(false);
          setIsLoadingOrganization(false);
          setIsInitialCheckComplete(true);
          return;
        }
      } catch (error) {
        console.error(
          "SessionInitializer: Critical error during auth check:",
          error
        );
        clearUser();
        clearOrganization();
        router.replace(loginRoute);
      } finally {
        setIsLoadingAuth(false);
        setIsLoadingOrganization(false);
        setIsInitialCheckComplete(true);
      }
    };

    performAuthCheck();
  }, [
    router,
    pathname,
    searchParams,
    setIsLoadingAuth,
    setIsLoadingOrganization,
    clearUser,
    clearOrganization,
    setIsInitialCheckComplete,
  ]);

  if (isLoadingAuth || !isInitialCheckComplete || isLoadingOrganization) {
    return <>{children}</>; // Return children while loading to prevent layout shift
  }

  return <>{children}</>;
}

export function SessionInitializer({ children }: SessionInitializerProps) {
  return (
    <Suspense fallback={<>{children}</>}>
      <SessionInitializerContent>{children}</SessionInitializerContent>
    </Suspense>
  );
}
