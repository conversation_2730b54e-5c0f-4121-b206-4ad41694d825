"use client";

import React from "react";
import { HelpCircleIcon, SearchIcon, WandSparklesIcon } from "lucide-react"; // Assuming SearchIcon exists or use an appropriate one
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { faqs } from "@/shared/constants";

interface HelpModalProps {
  children: React.ReactNode; // Trigger element
}

export const HelpModal: React.FC<HelpModalProps> = ({ children }) => {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="min-w-[800px] p-6 text-brand-primary-font">
        <DialogHeader>
          <DialogTitle className="text-2xl font-primary font-semibold ">
            Help
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          {/* Search Bar Placeholder */}
          <div className="relative">
            <Input
              type="search"
              placeholder="Ask our 24/7 AI Chatbot any question"
              className="pr-10 h-11 font-primary placeholder:font-primary" // Add padding for icon
              // Add onChange, value etc. if implementing search
            />
            <WandSparklesIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          </div>

          {/* FAQs Section */}
          <div>
            <h3 className="text-lg font-semibold mb-3">FAQs</h3>
            <Accordion type="single" collapsible className="w-full">
              {faqs.map((faq, index) => (
                <AccordionItem value={`item-${index}`} key={index}>
                  <AccordionTrigger className="text-sm text-left hover:no-underline">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-sm text-muted-foreground">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-3">
              For further information, you can go through our:
            </h3>
            <ul className="space-y-4 text-brand-secondary underline">
              <li>
                <a href="#">
                  <a className="text-sm">- Product Docs</a>
                </a>
              </li>
              <li>
                <a href="#" className="text-sm">
                  - YouTube Demo Playlist
                </a>
              </li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
