"use client";

import { cn } from "@/lib/utils";
import { useUserStore } from "@/hooks/use-user";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { authApi } from "@/app/api/auth";
import { useState } from "react";
import { LoaderIcon } from "lucide-react";
import Image from "next/image";
import { userSettingsRoute } from "@/shared/routes";
import Link from "next/link";

interface UserAvatarProps {
  className?: string;
  fallbackClassName?: string;
}

export const UserAvatar = ({
  className,
  fallbackClassName,
}: UserAvatarProps) => {
  const { user } = useUserStore();
  const [loading, setLoading] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Generate initials from user's fullName
  const getInitials = (fullName?: string | null): string => {
    if (!fullName) return "U";

    const names = fullName.trim().split(" ");
    if (names.length === 1) return names[0].charAt(0).toUpperCase();

    return (
      names[0].charAt(0) + names[names.length - 1].charAt(0)
    ).toUpperCase();
  };

  // Handle edge case where user store might be deleted from storage
  const initials = user?.fullName ? getInitials(user.fullName) : "U";

  const handleLogout = async () => {
    try {
      setLoading(true);
      await authApi.logout();
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  const renderAvatar = () => {
    if (loading) {
      return <LoaderIcon className="w-4 h-4 animate-spin text-brand-primary" />;
    }

    if (user?.profileImage && !imageError) {
      return (
        <div
          className={cn(
            "relative flex cursor-pointer items-center justify-center overflow-hidden rounded-sm",
            className
          )}
        >
          <Image
            src={user.profileImage}
            alt={user.fullName || "User"}
            fill
            className="object-cover"
            onError={() => setImageError(true)}
          />
        </div>
      );
    }

    return (
      <div
        className={cn(
          "relative flex cursor-pointer items-center justify-center overflow-hidden rounded-sm",
          className
        )}
      >
        <div
          className={cn(
            "flex h-full w-full items-center justify-center bg-brand-primary text-white",
            fallbackClassName
          )}
        >
          <span className="text-sm font-medium">{initials}</span>
        </div>
      </div>
    );
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{renderAvatar()}</DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        sideOffset={10}
        alignOffset={20}
        className="p-2"
      >
        <DropdownMenuItem>
          <Link className="text-base font-primary" href={userSettingsRoute}>
            Settings
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem
          className="text-base font-primary text-red-500 cursor-pointer"
          onClick={handleLogout}
        >
          Logout
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
