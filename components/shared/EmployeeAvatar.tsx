import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { cn } from "@/lib/utils";
import { EmployeeAvatarProps } from "@/shared/interfaces";

export const EmployeeAvatar = ({
  src,
  name,
  className,
  onClick,
}: EmployeeAvatarProps) => {
  const fallbackInitial = name ? name.charAt(0).toUpperCase() : "?";

  return (
    <Avatar
      className={cn(
        className,
        onClick && "cursor-pointer hover:opacity-80 transition-opacity"
      )}
      onClick={onClick}
    >
      <AvatarImage src={src} />
      <AvatarFallback>{fallbackInitial}</AvatarFallback>
    </Avatar>
  );
};
