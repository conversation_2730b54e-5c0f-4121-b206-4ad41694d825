// Custom separator component for the platform
"use client";

import { cn } from "@/lib/utils";
import { CustomSeparatorProps } from "@/shared/interfaces";
import React from "react";

export const CustomSeparator = ({ className }: CustomSeparatorProps) => {
  return (
    <div
      className={cn(
        "w-[80%] h-[2px] rounded-[2px] bg-brand-stroke mx-auto",
        className
      )}
    ></div>
  );
};
