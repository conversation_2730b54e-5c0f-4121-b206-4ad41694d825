import * as React from "react";
import { <PERSON>, Sun } from "lucide-react";
import { useTheme } from "next-themes";

export function ModeToggle() {
  const { setTheme, theme, resolvedTheme } = useTheme();

  const currentTheme = resolvedTheme || theme;

  return (
    <button
      onClick={() => setTheme(currentTheme === "dark" ? "light" : "dark")}
      className="flex items-center gap-2 rounded-full bg-brand-card-hover hover:opacity-80 border-2 border-brand-stroke py-1 px-[6px] cursor-pointer"
    >
      {currentTheme === "dark" ? (
        <Moon className="h-3 w-3" fill="currentColor" />
      ) : (
        <Sun className="h-3 w-3 " fill="currentColor" />
      )}
      <span className="uppercase font-semibold text-brand-primary-font text-[6px]">
        {currentTheme === "dark" ? "Dark" : "Light"}
      </span>
      <span className="sr-only">Toggle theme</span>
    </button>
  );
}
