"use client";

import { SearchIcon } from "lucide-react";
import { Input } from "../ui/input";

interface SearchBarProps {
  onSearch: (query: string) => void;
}

export const SearchBar = ({ onSearch }: SearchBarProps) => {
  return (
    <div className="flex items-center w-full  border border-brand-input rounded-md text-brand-primary-font font-primary font-medium px-2 py-[1px]">
      <SearchIcon className="h-4 w-4 shrink-0 text-brand-primary-font disabled:opacity-50" />
      <Input
        type="text"
        placeholder="Search..."
        className="flex-1 min-w-0 placeholder:text-brand-primary-font border-none focus:outline-none focus:ring-0 text-base bg-transparent select-none focus-visible:outline-none focus-visible:ring-0"
        onChange={(e) => onSearch(e.target.value)}
      />
    </div>
  );
};
