"use client";

import React, { useState, useRef } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { DialogCarouselProps } from "@/shared/interfaces";

const DialogCarousel = ({ images }: DialogCarouselProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const carouselRef = useRef<HTMLDivElement>(null);

  const goToNext = () => {
    if (!isTransitioning && currentIndex < images.length - 1) {
      setIsTransitioning(true);
      setCurrentIndex(currentIndex + 1);
    }
  };

  const goToPrev = () => {
    if (!isTransitioning && currentIndex > 0) {
      setIsTransitioning(true);
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleTransitionEnd = () => {
    setIsTransitioning(false);
  };

  return (
    <div className="relative overflow-hidden ">
      <div
        ref={carouselRef}
        className="flex transition-transform duration-300 ease-in-out"
        style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        onTransitionEnd={handleTransitionEnd}
      >
        {images.map((image, index) => (
          <div
            key={index}
            className="min-w-full h-[200px] flex-shrink-0 overflow-hidden"
          >
            <Image
              src={image}
              alt={`Slide ${index}`}
              height={200}
              width={300}
              className="w-fit h-full object-contain border-2 border-black rounded-lg mx-auto"
            />
          </div>
        ))}
      </div>

      <button
        onClick={goToPrev}
        disabled={currentIndex === 0}
        className={cn(
          "absolute left-2 top-1/2 -translate-y-1/2 cursor-pointer",
          currentIndex === 0
            ? "opacity-50 cursor-not-allowed"
            : "opacity-100 cursor-pointer"
        )}
      >
        <ChevronLeft
          size={30}
          strokeWidth={1.2}
          className="text-brand-primary-font"
        />
      </button>

      <button
        onClick={goToNext}
        disabled={currentIndex === images.length - 1}
        className={cn(
          "absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer",
          currentIndex === images.length - 1
            ? "opacity-50 cursor-not-allowed"
            : "opacity-100 cursor-pointer"
        )}
      >
        <ChevronRight
          size={30}
          strokeWidth={1.2}
          className="text-brand-primary-font"
        />
      </button>
    </div>
  );
};

export default DialogCarousel;
