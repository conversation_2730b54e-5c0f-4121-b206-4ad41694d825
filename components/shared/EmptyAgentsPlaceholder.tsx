"use client";

import { emptyAgentsContainer } from "@/shared/constants";
import Image from "next/image";
import { PrimaryButton } from "./PrimaryButton";
import { PlusIcon } from "lucide-react";
import { CreateEmployeeModal } from "@/app/(platform)/dashboard/_components/CreateEmployeeModal";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";

export const EmptyAgentsPlaceholder = () => {
  const { openModal } = useEmployeeCreateStore();

  return (
    <div className="py-14 flex flex-col gap-10 w-full justify-center items-center">
      <h2 className="text-brand-primary-font text-xl font-semibold text-center">
        Your workforce is empty. Click the button below to get started with
        adding/creating employees.
      </h2>
      <Image
        src={emptyAgentsContainer}
        alt="Empty Bench Container"
        width={250}
        height={250}
      />
      <PrimaryButton className="flex gap-2 w-[200px]" onClick={openModal}>
        <PlusIcon className="w-4 h-4" />
        Create Employee
      </PrimaryButton>
      <CreateEmployeeModal />
    </div>
  );
};
