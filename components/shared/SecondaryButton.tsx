// Custom button component for the secondary button with loading states and dynamic styles
"use client";

import { cn } from "@/lib/utils";
import { Button } from "../ui/button";
import React from "react";
import { Loader2 } from "lucide-react";
import { SecondaryButtonProps } from "@/shared/interfaces";

export const SecondaryButton = ({
  children,
  className,
  onClick,
  isLoading,
  disabled,
}: SecondaryButtonProps) => {
  return (
    <Button
      variant="secondary"
      className={cn(className)}
      onClick={onClick}
      disabled={isLoading || disabled}
    >
      {isLoading && <Loader2 className="mr-2  h-[32px] w-4 animate-spin" />}
      {children}
    </Button>
  );
};
