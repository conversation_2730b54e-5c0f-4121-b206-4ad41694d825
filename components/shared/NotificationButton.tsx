"use client";

import { BellIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useNotificationStore } from "@/hooks/use-notification";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

interface NotificationButtonProps {
  className?: string;
}

export const NotificationButton = ({ className }: NotificationButtonProps) => {
  const { isNotificationOpen, toggleNotification } = useNotificationStore();

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <button
          onClick={toggleNotification}
          className={cn(
            "flex items-center justify-center w-9 h-9 rounded-sm transition-all duration-100 ease-in",
            isNotificationOpen
              ? "bg-brand-clicked text-brand-primary"
              : "hover:bg-brand-card-hover hover:text-brand-primary",
            className
          )}
          aria-label="Notifications"
        >
          <BellIcon size={24} strokeWidth={1.2} />
        </button>
      </TooltipTrigger>
      <TooltipContent side="right">Notifications</TooltipContent>
    </Tooltip>
  );
};
