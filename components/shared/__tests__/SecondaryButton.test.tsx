import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";

import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { SecondaryButtonProps } from "@/shared/interfaces";

// This mock assumes 'cn' simply joins truthy class names with spaces.
jest.mock("@/lib/utils", () => ({
  cn: (...args: (string | undefined | null | boolean)[]) =>
    args.filter(Boolean).join(" "),
}));

// mocking the button component to ensure our SecondaryButton passes the correct props like children, className, and onClick.
jest.mock("@/components/ui/button", () => ({
  Button: jest.fn(
    ({
      children,
      className,
      onClick,
      disabled,
    }: {
      children: React.ReactNode;
      className?: string;
      onClick?: () => void;
      disabled?: boolean;
      variant?: string;
    }) => (
      <button className={className} onClick={onClick} disabled={disabled}>
        {children}
      </button>
    )
  ),
}));

// Group all tests for the SecondaryButton component
describe("SecondaryButton", () => {
  const defaultProps: Omit<SecondaryButtonProps, "isLoading"> = {
    children: "Click Me Secondary",
    onClick: jest.fn(),
    className: "",
  };

  beforeEach(() => {
    (defaultProps.onClick as jest.Mock).mockClear();
  });

  // Test case 1: Basic rendering
  it("renders the button with its children", () => {
    // Render the component with default props
    render(<SecondaryButton {...defaultProps} />);

    // Find the button element using its accessible role and name (text content).
    const button = screen.getByRole("button", { name: /click me secondary/i });

    // Assert that the button element is present in the rendered output
    expect(button).toBeInTheDocument();
    // Assert that the button contains the correct text content
    expect(button).toHaveTextContent("Click Me Secondary");
  });

  // Test case 2: onClick handler functionality
  it("calls the onClick handler when clicked", () => {
    // Render the component
    render(<SecondaryButton {...defaultProps} />);
    const button = screen.getByRole("button", { name: /click me secondary/i });

    // Simulate a user clicking the button
    fireEvent.click(button);

    // Assert that the mock onClick function was called exactly once
    expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
  });

  // Test case 3: Custom className application
  it("applies custom className passed via props", () => {
    const customClass = "my-secondary-class another-style";
    // Render the component with a custom className
    render(<SecondaryButton {...defaultProps} className={customClass} />);
    const button = screen.getByRole("button", { name: /click me secondary/i });

    // Assert that the button element has the custom classes.
    expect(button).toHaveClass(customClass);
  });
});
