import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";

import { Logo } from "@/components/shared/Logo";
import { fullLogoPath } from "@/shared/constants";

describe("Logo", () => {
  it("renders the logo image with correct attributes using the imported path", () => {
    render(<Logo />);

    const logoImage = screen.getByAltText("Logo");

    expect(logoImage).toBeInTheDocument();

    // Verify the src attribute matches the imported constant
    expect(logoImage).toHaveAttribute("src", fullLogoPath);
    expect(logoImage).toHaveAttribute("width", "80");
    expect(logoImage).toHaveAttribute("height", "80");
  });
});
