import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";

import { UserAvatar } from "@/components/shared/UserAvatar";

// Mock the useUserStore hook
jest.mock("@/hooks/use-user", () => ({
  useUserStore: jest.fn(() => ({
    user: {
      fullName: "John Doe",
      email: "<EMAIL>",
    },
  })),
}));

// Mock the cn utility
jest.mock("@/lib/utils", () => ({
  cn: (...inputs: any[]) => inputs.filter(Boolean).join(" "),
}));

describe("UserAvatar", () => {
  it("renders the avatar with initials from user's fullName", () => {
    render(<UserAvatar />);

    // Should show initials when no profile image
    const initialsElement = screen.getByText("JD"); // Initials from "John Doe"
    expect(initialsElement).toBeInTheDocument();
  });

  it("applies custom className to the container div", () => {
    const customClass = "custom-avatar-style";
    render(<UserAvatar className={customClass} />);

    const container = screen.getByText("JD").closest("div")?.parentElement;
    expect(container).toHaveClass(customClass);
  });

  it("applies custom fallbackClassName to the initials container", () => {
    const customFallbackClass = "custom-fallback-style";
    render(<UserAvatar fallbackClassName={customFallbackClass} />);

    const initialsContainer = screen.getByText("JD").closest("div");
    expect(initialsContainer).toHaveClass(customFallbackClass);
  });

  it("renders image when profileImage is available", () => {
    // Override the mock for this specific test
    jest.requireMock("@/hooks/use-user").useUserStore.mockReturnValueOnce({
      user: {
        fullName: "John Doe",
        profileImage: "/path/to/image.jpg",
      },
      isLoggedIn: true,
    });

    render(<UserAvatar />);
    const image = screen.getByTestId("next-image");
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute("src", "/path/to/image.jpg");
  });

  // Test with different user name scenarios
  it("handles single name correctly", () => {
    // Override the mock for this specific test
    jest.requireMock("@/hooks/use-user").useUserStore.mockReturnValueOnce({
      user: { fullName: "John" },
      isLoggedIn: true,
    });

    render(<UserAvatar />);
    const initialsElement = screen.getByText("J");
    expect(initialsElement).toBeInTheDocument();
  });

  it("handles empty or null fullName correctly", () => {
    // Override the mock for this specific test
    jest.requireMock("@/hooks/use-user").useUserStore.mockReturnValueOnce({
      user: { fullName: null },
      isLoggedIn: true,
    });

    render(<UserAvatar />);
    const initialsElement = screen.getByText("U"); // Default for undefined/null
    expect(initialsElement).toBeInTheDocument();
  });

  it("handles case when user store is deleted from storage", () => {
    // Override the mock for this specific test to simulate deleted user store
    jest.requireMock("@/hooks/use-user").useUserStore.mockReturnValueOnce({
      user: null,
      isLoggedIn: false,
    });

    render(<UserAvatar />);
    const initialsElement = screen.getByText("U"); // Default for guest/deleted store
    expect(initialsElement).toBeInTheDocument();
  });
});
