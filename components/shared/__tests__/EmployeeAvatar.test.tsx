import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";

import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { EmployeeAvatarProps } from "@/shared/interfaces";

jest.mock("@/components/ui/avatar", () => ({
  Avatar: ({
    className,
    children,
  }: {
    className?: string;
    children: React.ReactNode;
  }) => (
    <div data-testid="avatar-root" className={className}>
      {children}
    </div>
  ),
  AvatarImage: ({ src }: { src?: string }) => (
    <img data-testid="avatar-image" src={src} alt="" />
  ),
  AvatarFallback: ({ children }: { children: React.ReactNode }) => (
    <span data-testid="avatar-fallback">{children}</span>
  ),
}));

// --- Test Suite ---

describe("EmployeeAvatar", () => {
  const defaultProps: EmployeeAvatarProps = {
    src: "/path/to/image.jpg",
    fallback: "JD", // Default fallback text for most tests
    className: "",
  };

  it("renders the AvatarImage with the correct src", () => {
    render(<EmployeeAvatar {...defaultProps} />);
    const image = screen.getByTestId("avatar-image");
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute("src", defaultProps.src);
  });

  it("renders the AvatarFallback with the provided fallback text", () => {
    render(<EmployeeAvatar {...defaultProps} />);
    const fallback = screen.getByTestId("avatar-fallback");
    expect(fallback).toBeInTheDocument();
    expect(fallback).toHaveTextContent(defaultProps.fallback as string);
  });

  it("renders the AvatarFallback with default 'AI' text when fallback prop is not provided", () => {
    // Render without the fallback prop
    render(<EmployeeAvatar src={defaultProps.src} />);
    const fallback = screen.getByTestId("avatar-fallback");
    expect(fallback).toBeInTheDocument();
    expect(fallback).toHaveTextContent("AI");
  });

  it("renders the AvatarFallback with default 'AI' text when fallback prop is explicitly undefined", () => {
    render(<EmployeeAvatar src={defaultProps.src} fallback={undefined} />);
    const fallback = screen.getByTestId("avatar-fallback");
    expect(fallback).toBeInTheDocument();
    expect(fallback).toHaveTextContent("AI");
  });

  it("applies custom className to the root Avatar component", () => {
    const customClass = "custom-avatar-style border-2";
    render(<EmployeeAvatar {...defaultProps} className={customClass} />);
    const avatarRoot = screen.getByTestId("avatar-root");
    expect(avatarRoot).toHaveClass(customClass);
  });
});
