import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { useTheme } from "next-themes";
import { ModeToggle } from "@/components/shared/ModeToggle";

// Mock the icons from lucide-react
jest.mock("lucide-react", () => ({
  Moon: (props: any) => <div data-testid="moon-icon" {...props} />,
  Sun: (props: any) => <div data-testid="sun-icon" {...props} />,
}));

// Mock the useTheme hook from next-themes
jest.mock("next-themes", () => ({
  useTheme: jest.fn(), // Mock the hook function itself
}));

// --- Test Suite ---

describe("ModeToggle", () => {
  // Create a mock setTheme function to track calls
  const mockSetTheme = jest.fn();

  // Helper function to set up the mock return value for useTheme
  const setupMockTheme = (currentTheme: "light" | "dark" | "system") => {
    // Determine resolvedTheme based on currentTheme for simplicity in tests
    // (In reality, 'system' would resolve based on OS settings)
    const resolved = currentTheme === "system" ? "light" : currentTheme; // Default system to light for test predictability if needed, or dark
    (useTheme as jest.Mock).mockReturnValue({
      theme: currentTheme,
      resolvedTheme: resolved, // Use resolvedTheme as the component does
      setTheme: mockSetTheme,
    });
  };

  beforeEach(() => {
    // Clear mock calls before each test
    mockSetTheme.mockClear();
    (useTheme as jest.Mock).mockClear(); // Clear the hook mock itself if needed
  });

  it("renders Sun icon and 'Light' text when theme is light", () => {
    setupMockTheme("light");
    render(<ModeToggle />);

    expect(screen.getByTestId("sun-icon")).toBeInTheDocument();
    expect(screen.queryByTestId("moon-icon")).not.toBeInTheDocument();
    expect(screen.getByText("Light")).toBeInTheDocument();
    expect(screen.queryByText("Dark")).not.toBeInTheDocument();
    expect(screen.getByText("Toggle theme")).toBeInTheDocument(); // Check for sr-only text
  });

  it("renders Moon icon and 'Dark' text when theme is dark", () => {
    setupMockTheme("dark");
    render(<ModeToggle />);

    expect(screen.getByTestId("moon-icon")).toBeInTheDocument();
    expect(screen.queryByTestId("sun-icon")).not.toBeInTheDocument();
    expect(screen.getByText("Dark")).toBeInTheDocument();
    expect(screen.queryByText("Light")).not.toBeInTheDocument();
    expect(screen.getByText("Toggle theme")).toBeInTheDocument();
  });

  it("renders based on resolvedTheme when theme is system (e.g., resolved to dark)", () => {
    // Simulate 'system' resolving to 'dark'
    (useTheme as jest.Mock).mockReturnValue({
      theme: "system",
      resolvedTheme: "dark",
      setTheme: mockSetTheme,
    });
    render(<ModeToggle />);

    expect(screen.getByTestId("moon-icon")).toBeInTheDocument();
    expect(screen.getByText("Dark")).toBeInTheDocument();
  });

  it("calls setTheme with 'dark' when clicked and current theme is light", () => {
    setupMockTheme("light");
    render(<ModeToggle />);

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(mockSetTheme).toHaveBeenCalledTimes(1);
    expect(mockSetTheme).toHaveBeenCalledWith("dark");
  });

  it("calls setTheme with 'light' when clicked and current theme is dark", () => {
    setupMockTheme("dark");
    render(<ModeToggle />);

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(mockSetTheme).toHaveBeenCalledTimes(1);
    expect(mockSetTheme).toHaveBeenCalledWith("light");
  });
});
