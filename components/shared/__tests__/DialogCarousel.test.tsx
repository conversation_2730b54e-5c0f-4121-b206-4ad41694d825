import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import DialogCarousel from "../DialogCarousel";

// Mock next/image
jest.mock("next/image", () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line jsx-a11y/alt-text
    return <img {...props} />;
  },
}));

// Mock the cn utility
jest.mock("@/lib/utils", () => ({
  cn: (...args: (string | undefined | null | boolean)[]) =>
    args.filter(Boolean).join(" "),
}));

describe("DialogCarousel", () => {
  const mockImages = ["/image1.jpg", "/image2.jpg", "/image3.jpg"];

  it("renders the carousel with images", () => {
    render(<DialogCarousel images={mockImages} />);

    // Check if all images are rendered
    const images = screen.getAllByRole("img");
    expect(images).toHaveLength(mockImages.length);

    // Check if the first image has the correct src
    expect(images[0]).toHaveAttribute("src", mockImages[0]);
  });

  it("disables the previous button on the first slide", () => {
    const { container } = render(<DialogCarousel images={mockImages} />);

    // Find the previous button using container query since it doesn't have an accessible name
    const prevButton = container.querySelector("button[disabled]");

    // Check if it's disabled
    expect(prevButton).toBeInTheDocument();
    expect(prevButton).toHaveAttribute("disabled");
    expect(prevButton).toHaveClass("opacity-50");
    expect(prevButton).toHaveClass("cursor-not-allowed");
  });

  it("enables the next button when not on the last slide", () => {
    const { container } = render(<DialogCarousel images={mockImages} />);

    // Find the next button using container query
    const nextButton = container.querySelector("button:not([disabled])");

    // Check if it's enabled
    expect(nextButton).toBeInTheDocument();
    expect(nextButton).not.toHaveAttribute("disabled");
    expect(nextButton).toHaveClass("opacity-100");
    expect(nextButton).toHaveClass("cursor-pointer");
  });

  it("navigates to the next slide when clicking the next button", () => {
    const { container } = render(<DialogCarousel images={mockImages} />);

    // Find the next button and click it
    const nextButton = container.querySelector("button:not([disabled])");
    expect(nextButton).toBeInTheDocument();
    if (nextButton) {
      fireEvent.click(nextButton);
    }

    // Check if the carousel has moved to the next slide
    const carouselContainer = container.querySelector(".flex");
    expect(carouselContainer).toHaveStyle("transform: translateX(-100%)");
  });

  it("navigates to the previous slide after going to the next slide", () => {
    const { container } = render(<DialogCarousel images={mockImages} />);

    // Go to the next slide first
    const nextButton = container.querySelector("button:not([disabled])");
    expect(nextButton).toBeInTheDocument();
    if (nextButton) {
      fireEvent.click(nextButton);
    }

    // Simulate the transition end event
    const carouselContainer = container.querySelector(".flex");
    expect(carouselContainer).toBeInTheDocument();
    if (carouselContainer) {
      fireEvent.transitionEnd(carouselContainer);
    }

    // Now go back to the previous slide
    const prevButton = container.querySelectorAll("button")[0]; // First button should be prev
    expect(prevButton).toBeInTheDocument();
    if (prevButton) {
      fireEvent.click(prevButton);
    }

    // Check if the carousel has moved back to the first slide
    // Note: The actual style might be -0% which is equivalent to 0%
    expect(carouselContainer).toHaveStyle("transform: translateX(-0%)");
  });

  it("disables the next button on the last slide", () => {
    const { container } = render(<DialogCarousel images={mockImages} />);

    // Get the next button
    const nextButton = container.querySelector("button:not([disabled])");
    expect(nextButton).toBeInTheDocument();

    // Click through all slides
    if (nextButton) {
      for (let i = 0; i < mockImages.length - 1; i++) {
        fireEvent.click(nextButton);

        // Simulate the transition end event
        const carouselContainer = container.querySelector(".flex");
        if (carouselContainer) {
          fireEvent.transitionEnd(carouselContainer);
        }
      }
    }

    // After going through all slides, the next button should be disabled
    // We need to query again as the button state has changed
    const buttons = container.querySelectorAll("button");
    const lastNextButton = buttons[1]; // Second button should be next

    // Check if the next button is now disabled
    expect(lastNextButton).toHaveAttribute("disabled");
  });

  it("renders a single image correctly", () => {
    const singleImage = ["/single-image.jpg"];
    const { container } = render(<DialogCarousel images={singleImage} />);

    // Check if the image is rendered
    const image = container.querySelector("img");
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute("src", singleImage[0]);

    // Both buttons should be disabled
    const buttons = container.querySelectorAll("button");
    expect(buttons.length).toBe(2);

    // Check that both buttons are disabled
    expect(buttons[0]).toHaveAttribute("disabled");
    expect(buttons[1]).toHaveAttribute("disabled");
  });
});
