import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";

import { NotificationButton } from "@/components/shared/NotificationButton";

// Mock the useNotificationStore hook
jest.mock("@/hooks/use-notification", () => ({
  useNotificationStore: () => ({
    isNotificationOpen: false,
    toggleNotification: jest.fn(),
  }),
}));

// Mock the Tooltip component
jest.mock("@/components/ui/tooltip", () => ({
  Tooltip: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  TooltipTrigger: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  TooltipContent: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

describe("NotificationButton", () => {
  it("renders the notification button with bell icon", () => {
    render(<NotificationButton />);
    
    // Check if the button is rendered
    const button = screen.getByRole("button", { name: /notifications/i });
    expect(button).toBeInTheDocument();
    
    // Check if the bell icon is rendered
    const bellIcon = document.querySelector("svg");
    expect(bellIcon).toBeInTheDocument();
  });

  it("calls toggleNotification when clicked", () => {
    const { useNotificationStore } = require("@/hooks/use-notification");
    const toggleMock = jest.fn();
    
    // Override the mock implementation for this test
    useNotificationStore.mockReturnValue({
      isNotificationOpen: false,
      toggleNotification: toggleMock,
    });
    
    render(<NotificationButton />);
    
    // Click the button
    const button = screen.getByRole("button", { name: /notifications/i });
    fireEvent.click(button);
    
    // Check if toggleNotification was called
    expect(toggleMock).toHaveBeenCalledTimes(1);
  });

  it("applies active styles when notifications are open", () => {
    const { useNotificationStore } = require("@/hooks/use-notification");
    
    // Override the mock implementation for this test
    useNotificationStore.mockReturnValue({
      isNotificationOpen: true,
      toggleNotification: jest.fn(),
    });
    
    render(<NotificationButton />);
    
    // Check if the button has the active class
    const button = screen.getByRole("button", { name: /notifications/i });
    expect(button).toHaveClass("bg-brand-clicked");
    expect(button).toHaveClass("text-brand-primary");
  });
});
