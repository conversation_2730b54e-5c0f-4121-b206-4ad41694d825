import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";

import { CustomSeparator } from "@/components/shared/CustomSeparator";

// Mock the 'cn' utility function from '@/lib/utils'
jest.mock("@/lib/utils", () => ({
  cn: (...args: (string | undefined | null | boolean)[]) =>
    args.filter(Boolean).join(" "),
}));

describe("CustomSeparator", () => {
  const defaultClasses =
    "w-[80%] h-[2px] rounded-[2px] bg-brand-stroke mx-auto";

  it("renders with default classes when no className prop is provided", () => {
    // Render the component without any props
    const { container } = render(<CustomSeparator />);

    // The component renders a single div, so it should be the first child
    const separator = container.firstChild;

    expect(separator).toBeInTheDocument();
    // Check that the applied classes exactly match the default ones
    expect(separator).toHaveClass(defaultClasses, { exact: true });
  });

  it("renders with default and custom classes when className prop is provided", () => {
    const customClass = "my-custom-separator mt-4 mb-2";
    // Render the component with a custom className
    const { container } = render(<CustomSeparator className={customClass} />);

    const separator = container.firstChild;

    expect(separator).toBeInTheDocument();
    // Check that the applied classes include both default and custom ones, combined by the mocked 'cn'
    expect(separator).toHaveClass(`${defaultClasses} ${customClass}`, {
      exact: true,
    });
  });
});
