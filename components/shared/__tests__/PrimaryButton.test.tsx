import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";

import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { PrimaryButtonProps } from "@/shared/interfaces";

// This mock assumes 'cn' simply joins truthy class names with spaces.
jest.mock("@/lib/utils", () => ({
  cn: (...args: (string | undefined | null | boolean)[]) =>
    args.filter(Boolean).join(" "),
}));

// mocking the button component to ensure our PrimaryButton passes the correct props like children, className, and onClick.
jest.mock("@/components/ui/button", () => ({
  Button: jest.fn(
    ({
      children,
      className,
      onClick,
      disabled,
    }: {
      children: React.ReactNode;
      className?: string;
      onClick?: () => void;
      disabled?: boolean;
      variant?: string;
    }) => (
      <button
        className={className} // Pass className through
        onClick={onClick} // Pass onClick through
        disabled={disabled} // Pass disabled through
      >
        {children}
      </button>
    )
  ),
}));

// --- Test Suite ---

// Group all tests for the PrimaryButton component
describe("PrimaryButton", () => {
  const defaultProps: Omit<PrimaryButtonProps, "isLoading"> = {
    children: "Click Me",
    onClick: jest.fn(),
    className: "",
  };

  beforeEach(() => {
    (defaultProps.onClick as jest.Mock).mockClear();
  });

  it("renders the button with its children", () => {
    // Render the component with default props
    render(<PrimaryButton {...defaultProps} />);

    // Find the button element using its accessible role and name (text content).
    const button = screen.getByRole("button", { name: /click me/i });

    // Assert that the button element is present in the rendered output
    expect(button).toBeInTheDocument();
    // Assert that the button contains the correct text content
    expect(button).toHaveTextContent("Click Me");
  });

  // Test case 2: onClick handler functionality
  it("calls the onClick handler when clicked", () => {
    // Render the component
    render(<PrimaryButton {...defaultProps} />);
    const button = screen.getByRole("button", { name: /click me/i });

    // Simulate a user clicking the button
    fireEvent.click(button);

    // Assert that the mock onClick function was called exactly once
    expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
  });

  // Test case 3: Custom className application
  it("applies custom className passed via props", () => {
    const customClass = "my-custom-class extra-style";
    // Render the component with a custom className
    render(<PrimaryButton {...defaultProps} className={customClass} />);
    const button = screen.getByRole("button", { name: /click me/i });

    // Assert that the button element has the custom classes.
    expect(button).toHaveClass(customClass);
  });
});
