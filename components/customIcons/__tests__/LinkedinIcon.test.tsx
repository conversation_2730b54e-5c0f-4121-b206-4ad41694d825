import React from "react";
import { render } from "@testing-library/react";
import "@testing-library/jest-dom";
import { LinkedInIcon } from "../LinkedinIcon";

describe("LinkedInIcon", () => {
  it("renders the LinkedIn icon SVG", () => {
    const { container } = render(<LinkedInIcon />);
    
    // Check if the SVG is rendered
    const svg = container.querySelector("svg");
    expect(svg).toBeInTheDocument();
    
    // Check if the SVG has the correct viewBox
    expect(svg).toHaveAttribute("viewBox", "0 0 18 19");
    
    // Check if the SVG has the correct dimensions
    expect(svg).toHaveAttribute("width", "18");
    expect(svg).toHaveAttribute("height", "19");
    
    // Check if the SVG has a path with the LinkedIn blue color
    const path = container.querySelector("path");
    expect(path).toHaveAttribute("fill", "#007AB9");
  });
});
