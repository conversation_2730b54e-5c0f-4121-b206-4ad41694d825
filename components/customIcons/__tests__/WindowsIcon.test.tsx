import React from "react";
import { render } from "@testing-library/react";
import "@testing-library/jest-dom";
import { WindowsIcon } from "../WindowsIcon";

describe("WindowsIcon", () => {
  it("renders the Windows icon SVG", () => {
    const { container } = render(<WindowsIcon />);
    
    // Check if the SVG is rendered
    const svg = container.querySelector("svg");
    expect(svg).toBeInTheDocument();
    
    // Check if the SVG has the correct viewBox
    expect(svg).toHaveAttribute("viewBox", "0 0 48 48");
    
    // Check if the SVG has a path with the Windows blue color
    const path = container.querySelector("path");
    expect(path).toHaveAttribute("fill", "#03A9F4");
    
    // Check if the path contains the Windows logo shape data
    expect(path).toHaveAttribute("d", expect.stringContaining("M20 25L6 25 6 37.073 20 38.994z"));
  });
});
