import React from "react";
import { render } from "@testing-library/react";
import "@testing-library/jest-dom";
import { GithubIcon } from "../GithubIcon";

describe("GithubIcon", () => {
  it("renders the GitHub icon SVG", () => {
    const { container } = render(<GithubIcon />);
    
    // Check if the SVG is rendered
    const svg = container.querySelector("svg");
    expect(svg).toBeInTheDocument();
    
    // Check if the SVG has the correct viewBox
    expect(svg).toHaveAttribute("viewBox", "0 0 256 249");
    
    // Check if the SVG has the correct preserveAspectRatio
    expect(svg).toHaveAttribute("preserveAspectRatio", "xMinYMin meet");
    
    // Check if the SVG has a path with the fill color
    const path = container.querySelector("g");
    expect(path).toHaveAttribute("fill", "#161614");
  });
});
