import React from "react";
import { render } from "@testing-library/react";
import "@testing-library/jest-dom";
import { GoogleIcon } from "../GoogleIcon";

describe("GoogleIcon", () => {
  it("renders the Google icon SVG", () => {
    const { container } = render(<GoogleIcon />);
    
    // Check if the SVG is rendered
    const svg = container.querySelector("svg");
    expect(svg).toBeInTheDocument();
    
    // Check if the SVG has the correct viewBox
    expect(svg).toHaveAttribute("viewBox", "0 0 24 24");
    
    // Check if the SVG has the correct dimensions and classes
    expect(svg).toHaveClass("h-5");
    expect(svg).toHaveClass("w-5");
    
    // Check if the SVG has the correct accessibility attributes
    expect(svg).toHaveAttribute("aria-hidden", "true");
    expect(svg).toHaveAttribute("focusable", "false");
    
    // Check if the SVG has paths with Google's brand colors
    const paths = container.querySelectorAll("path");
    expect(paths.length).toBe(4);
    
    // Check for Google's brand colors in the paths
    const colors = Array.from(paths).map(path => path.getAttribute("fill"));
    expect(colors).toContain("#4285F4"); // Google Blue
    expect(colors).toContain("#34A853"); // Google Green
    expect(colors).toContain("#FBBC05"); // Google Yellow
    expect(colors).toContain("#EA4335"); // Google Red
  });
});
