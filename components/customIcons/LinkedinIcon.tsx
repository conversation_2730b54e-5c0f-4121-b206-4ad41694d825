import React from "react";

export function LinkedInIcon() {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_673_70098)">
        <path
          d="M9 18.1299C13.9706 18.1299 18 14.1004 18 9.12988C18 4.15932 13.9706 0.129883 9 0.129883C4.02944 0.129883 0 4.15932 0 9.12988C0 14.1004 4.02944 18.1299 9 18.1299Z"
          fill="#007AB9"
        />
        <path
          d="M14.3776 9.8537V13.5642H12.2263V10.1024C12.2263 9.23315 11.9157 8.63954 11.1368 8.63954C10.5424 8.63954 10.1893 9.03918 10.0334 9.42615C9.97672 9.56444 9.96212 9.75648 9.96212 9.95045V13.564H7.8107C7.8107 13.564 7.83958 7.70085 7.8107 7.09393H9.96228V8.0108C9.95795 8.01802 9.95185 8.02508 9.948 8.03198H9.96228V8.0108C10.2482 7.57089 10.758 6.942 11.9011 6.942C13.3165 6.942 14.3776 7.86673 14.3776 9.8537ZM5.56014 3.9751C4.82423 3.9751 4.34277 4.45816 4.34277 5.09284C4.34277 5.71404 4.81028 6.21106 5.53191 6.21106H5.54586C6.29621 6.21106 6.76275 5.71404 6.76275 5.09284C6.74847 4.45816 6.29621 3.9751 5.56014 3.9751ZM4.47064 13.5642H6.62125V7.09393H4.47064V13.5642Z"
          fill="#F1F2F2"
        />
      </g>
      <defs>
        <clipPath id="clip0_673_70098">
          <rect
            width="18"
            height="18"
            fill="white"
            transform="translate(0 0.129883)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
