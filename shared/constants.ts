import {
  HomeIcon,
  SettingsIcon,
  BotIcon,
  MessageSquareMoreIcon,
  VideoIcon,
  GlobeIcon,
  NotebookPenIcon,
  ClipboardList,
  UserPen,
  HistoryIcon,
  FolderKanban,
  Users,
  BookOpen,
  Tags,
  Workflow,
  UserIcon,
  NotepadTextIcon,
  Palette,
  Layers,
  BookOpenText,
  MessagesSquare,
  Headset,
} from "lucide-react";
import { CarouselSlide, Notification } from "./interfaces";
import { dashboardRoute, employeesRoute, settingsRoute } from "./routes";

// logos path constants
export const fullLogoPath = "/assets/logos/ruh-full-logo.svg";
export const noTextLogoPath = "/assets/logos/ruh-logo-no-text.svg";
export const emptyBenchContainer = "/assets/dashboard/empty_bench_employee.svg";
export const emptyAgentsContainer = "/assets/dashboard/empty_agents.svg";

// Mock notification data for demonstration . TODO: this will come from backend

export const MockNotifications: Notification[] = [
  // {
  //   id: "1",
  //   title: "New message from <PERSON>R Assistant",
  //   description: "Your leave request has been approved",
  //   time: "2 hours ago",
  //   read: false,
  // },
  // {
  //   id: "2",
  //   title: "System update",
  //   description: "The system will be down for maintenance tonight",
  //   time: "Yesterday",
  //   read: true,
  // },
  // {
  //   id: "3",
  //   title: "New employee added",
  //   description: "Marketing Assistant has been added to your workspace",
  //   time: "3 days ago",
  //   read: true,
  // },
];

// carousel images for auth carousel component
export const carouselSlides: CarouselSlide[] = [
  {
    image: "/assets/carousel/carousel-1.svg",
    title: "Digitalize Your Workforce with AI Employees You Can Build",
  },
  {
    image: "/assets/carousel/carousel-2.svg",
    title: "Automate Your Everyday Work with Intelligent AI-Powered Workflows",
  },
  {
    image: "/assets/carousel/carousel-3.svg",
    title: "Connect Your AI Employees Across Multiple Communication Channels",
  },
];

// Left bar items for the dashboard
export const leftBarItems = [
  {
    name: "Home",
    icon: HomeIcon,
    // href: dashboardRoute,
    href: dashboardRoute,
  },
  {
    name: "Employees",
    icon: BotIcon,
    href: employeesRoute,
  },
  {
    name: "Settings",
    icon: SettingsIcon,
    href: settingsRoute,
  },
];

// Chat Navbar Tabs
export const chatNavbarTabs = [
  {
    label: "Chat",
    value: "Chat",
    icon: MessageSquareMoreIcon,
  },
  // {
  //   label: "Workflow",
  //   value: "Workflow",
  //   icon: WorkflowIcon,
  // },
  {
    label: "Conversation History",
    value: "Conversation History",
    icon: HistoryIcon,
  },
];

// Employee Chat Tools
export const tools = [
  {
    label: "Blog Generation",
    toolIcon: NotebookPenIcon,
    id: "blog-generation",
    placeholder: "Enter your blog idea here. What do you have in mind?",
  },
  {
    label: "Video Generation",
    toolIcon: VideoIcon,
    id: "video-generation",
    placeholder: "Enter your video idea here. What do you have in mind?",
  },
  {
    label: "Social Media Creation and Posting",
    toolIcon: GlobeIcon,
    id: "social-media-creation-and-posting",
    placeholder: "Enter your social media idea here. What do you have in mind?",
  },
];

// TODO: Actual images will be coming from database this is dummy data for now
export const employeeCarouselImages = [
  "/assets/carousel/add-carousel-test.png",
  "/assets/carousel/add-carousel-test.png",
  "/assets/carousel/add-carousel-test.png",
];

// TODO: Add connected apps, this is just dummy data for now
export const connectedApps = [];

//Connected Apps icon and text
export const connectedAppsData = [
  {
    id: "gmail",
    name: "Gmail",
    icon: "/assets/icons/GmailIcon.svg",
    connected: false,
  },
  {
    id: "whatsapp",
    name: "WhatsApp",
    icon: "/assets/icons/WhatsappIcon.svg",
    connected: false,
  },
  {
    id: "slack",
    name: "Slack",
    icon: "/assets/icons/SlackIcon.svg",
    connected: false,
  },
];

//Employee Sidebar Buttons
export const employeeButtons = [
  {
    icon: ClipboardList,
    text: "View all employee",
    path: "/dashboard/employees",
  },
  {
    icon: UserPen,
    text: "Create employee",
    // path: "/dashboard/employees/create",
    path: "/dashboard",
  },
  // {
  //   icon: UserPlus,
  //   text: "Add employee",
  //   // path: "/dashboard/employees/add",
  //   path: "/dashboard",
  // },
];

//Settings Sidebar Buttons
export const settingsButtons = [
  {
    icon: Tags,
    text: "Workspace Settings",
    path: "/dashboard/settings",
  },

  {
    icon: FolderKanban,
    text: "Manage Departments",
    path: "/dashboard/settings/manage-departments",
  },

  {
    icon: Users,
    text: "Manage Agents",
    path: "/dashboard/settings/manage-agents",
  },
  {
    icon: Users,
    text: "Manage Members",
    path: "/dashboard/settings/manage-members",
  },
  {
    icon: BookOpen,
    text: "Knowledge Base",
    path: "/dashboard/settings/knowledge-base",
  },
];

// User Settings Sidebar Buttons
export const userSettingsButtons = [
  {
    icon: UserIcon,
    text: "Profile",
    path: "/dashboard/settings",
  },
  {
    icon: NotepadTextIcon,
    text: "Plan & Billing",
    // path: "/dashboard/settings/billing",
    path: "/dashboard",
  },
  {
    icon: Workflow,
    text: "Workflow Customization",
    // path: "/dashboard/settings/workflow",
    path: "/dashboard",
  },
  {
    icon: Palette,
    text: "Appearance & Theme",
    // path: "/dashboard/settings/appearance",
    path: "/dashboard",
  },
  {
    icon: Layers,
    text: "Preferences",
    // path: "/dashboard/settings/preferences",
    path: "/dashboard",
  },
  {
    icon: BookOpenText,
    text: "Docs",
    // path: "/dashboard/settings/docs",
    path: "/dashboard",
  },
  {
    icon: MessagesSquare,
    text: "Notifications",
    // path: "/dashboard/settings/notifications",
    path: "/dashboard",
  },
  {
    icon: Headset,
    text: "Support",
    // path: "/dashboard/settings/support",
    path: "/dashboard",
  },
];

export const faqs = [
  {
    question:
      "What is the difference of usage between Chat mode and Workflow Mode?",
    answer:
      "Chat mode allows for freeform conversation, while Workflow mode guides the AI through specific, predefined tasks.",
  },
  {
    question:
      "How can I access all the workflows that an employee can execute?",
    answer:
      "You can view available workflows in the 'Workflows' section of the employee's profile or by asking the AI directly in Chat mode.",
  },
  {
    question: "How can I create a new chat with the employee?",
    answer:
      "Navigate to the employee's profile and click the 'Start Chat' button, or select the employee from the main chat interface.",
  },
];

export const unpublish_employee = "/assets/dashboard/unpublish_employee.svg";

export const pricingPlans = [
  {
    name: "Free Plan",
    price: "$0",
    features: [
      "600 Free Credits",
      "1 pre-built employee",
      "3 pre-built workflows",
      "Workflow creation access (limited to 5 workflows)",
      "Employee creation access (limited to 10 employees)",
    ],
  },
  {
    name: "Standard Plan",
    price: "$30",
    features: [
      "600 Free Credits",
      "1 pre-built employee",
      "3 pre-built workflows",
      "Workflow creation access (limited to 5 workflows)",
      "Employee creation access (limited to 10 employees)",
    ],
  },
  {
    name: "Pro Plan",
    price: "$60",
    features: [
      "600 Free Credits",
      "1 pre-built employee",
      "3 pre-built workflows",
      "Workflow creation access (limited to 5 workflows)",
      "Employee creation access (limited to 10 employees)",
    ],
  },
];
