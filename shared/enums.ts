// Enum for model providers
export enum ModelProvider {
  OPENAI = "openai",
  ANTHROPIC = "anthropic",
  META = "meta",
}

export enum OpenAIModelName {
  GPT4 = "gpt-4",
  GPT35TURBO = "gpt-3.5-turbo",
}

export enum AnthropicModelName {
  CLAUDE3OPUS = "claude-3-opus",
  CLAUDE3SONNET = "claude-3-sonnet",
}

export enum MetaModelName {
  LLAMA3 = "llama-3",
}

// Enum for model names
export enum ModelName {
  GPT4 = "gpt-4",
  GPT35TURBO = "gpt-3.5-turbo",
  CLAUDE3OPUS = "claude-3-opus",
  CLAUDE3SONNET = "claude-3-sonnet",
  LLAMA3 = "llama-3",
}

// Enum for employee tone
export enum EmployeeTone {
  PROFESSIONAL = "professional",
  FRIENDLY = "friendly",
  CASUAL = "casual",
  FORMAL = "formal",
  ENTHUSIASTIC = "enthusiastic",
}

// Enum for employee department
export enum EmployeeDepartment {
  ENGINEERING = "engineering",
  MARKETING = "marketing",
  SALES = "sales",
  CUSTOMER_SUPPORT = "customer_support",
  HUMAN_RESOURCES = "human_resources",
  FINANCE = "finance",
  OPERATIONS = "operations",
  GENERAL = "general",
}

// Enum for employee visibility
export enum EmployeeVisibility {
  PUBLIC = "public",
  PRIVATE = "private",
}

// Enum for employee status
export enum EmployeeStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum MCPCategory {
  LLM = "llm",
  MULTIMODAL = "multimodal",
  EMBEDDING = "embedding",
  VISION = "vision",
  RAG = "rag",
  AGENT = "agent",
  FINE_TUNING = "fine_tuning",
  PROMPT_ENGINEERING = "prompt_engineering",
  WORKFLOW = "workflow",
  INTEGRATION = "integration",
}

/**
 * Enum for message sender types
 */
export enum SenderType {
  UNSPECIFIED = "SENDER_TYPE_UNSPECIFIED",
  USER = "SENDER_TYPE_USER",
  ASSISTANT = "SENDER_TYPE_ASSISTANT",
}

export enum LivekitRoomTopics {
  INPUT = "lk.chat",
  NORMAL_OUTPUT_MESSAGE = "lk.transcription",
  WORKFLOW_UPDATES = "lk.workflow.updates",
  WORKFLOW_APPROVAL_INPUT = "lk.workflow.approval",
}

export enum StepsStatus {
  STARTED = "started",
  CONNECTING = "connecting",
  CONNECTED = "connected",
  DISCONNECTED = "disconnected",
  COMPLETED = "completed",
  PAUSED = "paused",
  FAILED = "failed",
  ERROR = "error",
  CANCELLED = "cancelled",
  APPROVED = "approved",
  TIME_LOGGED = "time_logged",
}

export enum WorkflowStatus {
  RUNNING = "running",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled",
  UNKNOWN = "unknown",
  WAITING_FOR_APPROVAL = "waiting_for_approval",
}

export enum ResultDataType {
  STRING = "string",
  NUMBER = "number",
  ARRAY = "array",
  OBJECT = "object",
}

export enum ApprovalDecision {
  APPROVE = "approve",
  REJECT = "reject",
}
