# Ruh App

![Ruh App Banner](public/assets/misc/banner.png)

## Overview

Ruh App is a modern web application built with [Next.js](https://nextjs.org) 15 and [React](https://react.dev) 19, featuring a robust UI component system powered by [shadcn/ui](https://ui.shadcn.com) and styled with [Tailwind CSS](https://tailwindcss.com).

## Tech Stack

- **Framework:** [Next.js](https://nextjs.org) 15.2.4
- **Runtime:** [React](https://react.dev) 19.0.0
- **Styling:** [Tailwind CSS](https://tailwindcss.com) 4.x
- **UI Components:** [shadcn/ui](https://ui.shadcn.com)
- **Form Handling:** [React Hook Form](https://react-hook-form.com) 7.55.0
- **Validation:** [Zod](https://zod.dev) 3.24.2
- **API Integration:** [Axios](https://axios-http.com) 1.8.4
- **Data Fetching:** [React Query](https://tanstack.com/query/latest) 5.74.2
- **Theme Management:** [next-themes](https://github.com/pacocoursey/next-themes)
- **Icons:** [Lucide React](https://lucide.dev/guide/packages/lucide-react)
- **State Management:** [Zustand](https://github.com/pmndrs/zustand) 5.0.3
- **Real-time Audio:** [LiveKit](https://livekit.io) for voice communication

## Prerequisites

Before you begin, ensure you have the following installed:

- [Node.js](https://nodejs.org) (LTS version recommended)
- [Yarn](https://yarnpkg.com) (recommended) or [npm](https://www.npmjs.com)

## Getting Started

1. **Clone the repository**

   ```bash
   git clone https://gitlab.rapidinnovation.tech/ruh.ai/ruh-app-fe.git
   cd ruh-app-fe
   ```

2. **Install dependencies** (Yarn is recommended)

   ```bash
   yarn install
   ```

3. **Start the development server**

   ```bash
   yarn dev
   ```

   The application will be available at [http://localhost:3000](http://localhost:3000).

## Development

### Key Features

- 🎨 Modern UI components with shadcn/ui
- 🌓 Dark/Light mode support
- 🎯 Type-safe development with TypeScript
- 🔄 Robust API integration with Axios and React Query
- 🔒 Secure authentication with token refresh
- 🎤 Real-time voice communication with AI agents via LiveKit

### Project Structure

```
ruh-app/
├── app/           # Next.js app directory
│   ├── (auth)/    # Authentication routes and components
│   ├── (platform)/# Platform routes and components
│   └── api/       # API client functions
├── components/    # Reusable UI components
│   ├── shared/    # Custom shared components
│   └── ui/        # shadcn/ui components
├── docs/          # Documentation files
├── hooks/         # Custom React hooks, for state management
├── lib/           # Utility functions and helpers
│   ├── providers/ # React providers (Query, Theme)
│   └── schemas/   # Zod validation schemas
├── public/        # Static assets
├── services/      # API services
│   ├── axios.ts   # Axios instance with interceptors
│   └── authCookies.ts # Authentication cookie management
└── shared/        # Shared constants, interfaces, and site config
```

> For detailed documentation on API integration, authentication flow, technical requirements, and project tasks, see the [docs folder](/docs/).

### Documentation

The project includes comprehensive documentation in the `docs/` folder:

- [Project Structure](/docs/project-structure.md) - Overview of the codebase organization and architecture
- [Technical Requirements](/docs/technical-requirements.md) - Detailed technical specifications and requirements
- [Task List](/docs/task-list.md) - Prioritized list of features and tasks to be implemented
- [AI Prompt](/docs/ai-prompt.md) - Guidelines for AI integration and prompting

### Available Scripts

- `yarn dev` - Start development server
- `yarn build` - Create production build
- `yarn start` - Start production server
- `yarn lint` - Run ESLint for code quality

## API Integration

The application uses a robust API integration layer with the following features:

### Firebase Cloud Messaging (FCM)

- **Push Notifications**: Integrated FCM for real-time push notifications
- **Token Management**: Automatic FCM token generation during user login
- **Background Handling**: Service worker setup for background notifications
- **Secure Storage**: Environment-based Firebase configuration with VAPID key
- **Permission Flow**: Graceful notification permission handling

### Axios Configuration

- **Centralized Instance**: All API requests use a single axios instance configured in `services/axios.ts`
- **Interceptors**: Automatic token handling with request/response interceptors
- **Error Handling**: Comprehensive error handling with specific status code responses
- **Token Refresh**: Automatic refresh of expired tokens using the `/api/v1/auth/access-token` endpoint

### React Query Integration

- **QueryProvider**: Global setup in `lib/providers/QueryProvider.tsx`
- **Data Fetching**: Efficient data fetching with caching and background updates
- **Mutations**: Form submissions and data updates using `useMutation` hook
- **Error Handling**: Consistent pattern for handling API errors with toast notifications

### Authentication Flow

- **Token Management**: Secure cookie-based token storage using Next.js `cookies()` API
- **Login/Signup**: Clean authentication API with social login options
- **Onboarding**: Required user profile completion before accessing the dashboard
- **Route Protection**: Next.js middleware for protecting routes based on authentication state

## Testing

This project uses [Jest](https://jestjs.io/) and [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/) for testing. Tests are located in `__tests__` directories within the component or module folders (e.g., `components/shared/__tests__/PrimaryButton.test.tsx`).

Available test commands:

- **Run all tests:**

  ```bash
  yarn test
  ```

- **Run tests in watch mode:**

  ```bash
  yarn test:watch
  ```

- **Run tests for a specific file:**
  ```bash
  yarn test file-name.test.tsx
  ```
  _Replace `file-name.test.tsx` with the actual name of the test file._

## Contributing

We welcome contributions! Please follow these steps:

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

### Development Guidelines

- Follow the existing code style and conventions
- Write meaningful commit messages
- Update documentation as needed
- Add appropriate tests for new features

---
